{"version": 3, "sources": ["assets/resources/i18n/en.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACpB,SAAS;IACT,QAAQ,EAAE,uCAAuC;IACjD,SAAS,EAAE,uBAAuB;IAClC,mBAAmB,EAAE,4DAA4D;IACjF,iBAAiB,EAAE,sBAAsB;IACzC,YAAY,EAAE,eAAe;IAC7B,UAAU,EAAE,cAAc;IAC1B,eAAe,EAAE,mBAAmB;IACpC,iBAAiB,EAAE,sBAAsB;IACzC,gBAAgB,EAAE,qBAAqB;IACvC,uBAAuB,EAAE,6BAA6B;IACtD,eAAe,EAAE,wBAAwB;IACzC,WAAW,EAAE,4DAA4D;IAEzE,gBAAgB,EAAE,oBAAoB;IACtC,sBAAsB,EAAE,6BAA6B;IACrD,QAAQ,EAAE,sCAAsC;IAEhD,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,OAAO;IACjB,SAAS,EAAE,OAAO;IAClB,SAAS,EAAE,OAAO;IAClB,UAAU,EAAE,QAAQ;IACpB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,QAAQ;IAChB,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,aAAa;IAC1B,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,aAAa;IAC1B,cAAc,EAAE,iBAAiB;IACjC,UAAU,EAAE,oBAAoB;IAChC,iBAAiB,EAAE,mBAAmB;IACtC,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IAEd,SAAS,EAAE,MAAM;IAEjB,OAAO,EAAE,YAAY;IACrB,KAAK,EAAE,OAAO;IAEd,UAAU,EAAE,qBAAqB;IACjC,WAAW,EAAE,mBAAmB;IAEhC,MAAM,EAAC,YAAY;IAEnB,UAAU,EAAC,kBAAkB;IAE7B,MAAM,EAAE,oBAAoB;IAC5B,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,iBAAiB;IACzB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,OAAO;IAEf,MAAM,EAAE,2TAA2T;IACnU,MAAM,EAAE,uEAAuE;IAC/E,MAAM,EAAE,mDAAmD;IAC3D,MAAM,EAAE,4DAA4D;IACpE,MAAM,EAAE,6FAA6F;IACrG,MAAM,EAAE,yIAAyI;IAEjJ,MAAM,EAAE,oBAAoB;IAC5B,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,gBAAgB;IACxB,MAAM,EAAE,iBAAiB;IACzB,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,gBAAgB;IAExB,MAAM,EAAE,8QAA8Q;IACtR,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,iDAAiD;IACzD,MAAM,EAAE,+EAA+E;IACvF,MAAM,EAAE,sRAAsR;CAGjS,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n    //这部分是通用的\n    kickout1: 'You have been asked to leave the room',//您被请出房间\n    LeaveRoom: 'The room is dissolved',//房间已解散\n    InsufficientBalance: 'The current balance is insufficient, please go to purchase',//余额不足\n    GameRouteNotFound: 'Game route not found',//游戏线路异常\n    NetworkError: 'network error',//网络异常\n    RoomIsFull: 'Room is full',//房间已满\n    EnterRoomNumber: 'Enter room number',//输入房间号\n    GetUserInfoFailed: 'get user info failed',//获取用户信息失败\n    RoomDoesNotExist: 'Room does not exist',//房间不存在\n    FailedToDeductGoldCoins: 'Failed to deduct gold coins',//扣除金币失败\n    ExitApplication: 'Are you sure to leave?',//完全退出游戏\n    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',//退出本局游戏\n\n    NotEnoughPlayers: 'Not enough players',//玩家数量不足\n    TheGameIsFullOfPlayers: 'The game is full of players',//玩家数量已满\n    kickout2: 'Whether to kick {0} out of the room?',//踢出玩家文案\n\n    upSeat: 'Join', //上座\n    downSeat: 'Leave', //下座\n    startGame: 'Start', //开始游戏\n    readyGame: 'Ready', //准备\n    cancelGame: 'Cancel', //取消准备\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    kickout3: 'Kick Out',\n    back: 'Back',//返回\n    leave: 'Leave', //退出\n    music: 'Music',  //音乐\n    sound: 'Sound', //音效\n    join: 'Join', //加入\n    create: 'Create', //创建\n    auto: 'Auto',\n    Room: 'Room',\n    room_number: 'Room Number', //房间号\n    copy: 'Copy', //复制\n    game_amount: 'Game Amount', //游戏费用\n    player_numbers: 'Player Numbers:', //玩家数量\n    room_exist: 'Room doesn’t exist',//房间不存在\n    enter_room_number: 'Enter room number',//输入房间号\n    free: 'Free',\n    players: 'Players', //玩家\n    Player: 'Player',\n    Tickets: 'Tickets',\n    Empty: 'Empty',\n\n    nextlevel: 'Next',//下一关\n\n    relevel: 'Play Again', //再来一局\n    rules: 'Rules',\n\n    danjiguize: \"Single Player Rules\",\n    lianjuguize: \"Multiplayer Rules\",\n\n    boting:\"Hosting...\",\n\n    roundstart:\"New Round Starts\",\n\n    dtips1: \"Game Introduction:\",\n    dtips2: \"Safe Zone:\",\n    dtips3: \"Mine Zone:\",\n    dtips4: \"Game Objective:\",\n    dtips5: \"Marking:\",\n    dtips6: \"Hint:\",\n\n    dinfo1: \"The game contains hidden tiles that can be: numbered tiles, blank tiles, or mines. Click to reveal tiles - numbered tiles indicate how many mines are adjacent to that tile. Blank tiles will trigger a chain reaction of automatic reveals until numbered tiles are encountered. Use these mechanics to clear the board.\",\n    dinfo2: \"Numbered tiles and blank tiles are collectively called the Safe Zone.\",\n    dinfo3: \"Revealing a mine tile will cause instant failure.\",\n    dinfo4: \"Reveal all safe tiles without triggering any mines to win.\",\n    dinfo5: \"Long-press to place a flag marker on suspected mine tiles. Marking doesn't reveal the tile.\",\n    dinfo6: \"Players get one free hint per game (max 4 uses). Clicking Hint will reveal one safe tile. Hints are game-specific and don't carry over.\",\n\n    ltips1: \"Game Introduction:\",\n    ltips2: \"Player Count:\",\n    ltips3: \"Turn Duration:\",\n    ltips4: \"Game Objective:\",\n    ltips5: \"Marking:\",\n    ltips6: \"Scoring Rules:\",\n\n    linfo1: \"Uses the same tile mechanics as Single Player mode. Each turn, all players simultaneously select tiles. After time expires or all players finish choosing, results are revealed with score adjustments. Game ends when all tiles are claimed. Compete for the highest score!\",\n    linfo2: \"2/3/4 Players\",\n    linfo3: \"20 Seconds\",\n    linfo4: \"Score points through gameplay mechanics to win.\",\n    linfo5: \"Long-press to mark potential mines. Correct mine markings grant bonus points.\",\n    linfo6: \"1. First player to select a tile each turn earns +1pt.\\n2. Click-reveal: +6pts for safe tiles, -12pts for mines.\\n3. Mark-reveal: +10pts for correctly flagged mines, 0pts for safe tiles.\\n4. Shared tiles: Points are split (e.g., two players click same safe tile = +3pts each).\"\n\n\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.en = language;"]}