{"version": 3, "sources": ["assets/scripts/util/Tools.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;AAEtF,+CAA8C;AAC9C,mCAAkC;AAE5B,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAA;IAmVA,CAAC;IAjVU,mBAAa,GAApB,UAAqB,UAAmB,EAAE,aAAwB,EAAE,WAAsB,EAAE,cAAyB;QACjH,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;IAC1F,CAAC;IACM,uBAAiB,GAAxB,UAAyB,UAAmB,EAAE,aAAwB,EAAE,WAAsB,EAAE,cAAyB;QACrH,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;IAC3F,CAAC;IAED,SAAS;IACT,yCAAyC;IACzC,eAAe;IACf,oBAAoB;IACpB,kBAAkB;IAClB,qBAAqB;IACN,yBAAmB,GAAlC,UAAmC,UAAmB,EAAE,OAAgB,EAAE,aAAwB,EAAE,WAAsB,EAAE,cAAyB;QACjJ,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAC,KAA0B;YACpE,IAAI,OAAO,EAAE;gBACT,2BAAY,CAAC,eAAe,EAAE,CAAC;aAClC;YAED,IAAI,aAAa,IAAI,IAAI,EAAE;gBACvB,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;aACpC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAClE,IAAI,WAAW,IAAI,IAAI,EAAE;gBACrB,WAAW,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;aAClC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAC,KAA0B;YACrE,IAAI,cAAc,IAAI,IAAI,EAAE;gBACxB,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;aACrC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEM,8BAAwB,GAA/B,UAAgC,UAAmB;QAC/C,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IACM,4BAAsB,GAA7B,UAA8B,UAAmB;QAC7C,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IACM,+BAAyB,GAAhC,UAAiC,UAAmB;QAChD,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,SAAS;IACF,wBAAkB,GAAzB,UAA0B,IAAa,EAAE,IAAY;QACjD,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,KAAY,EAAE,MAAsB;YAClF,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,QAAQ;IACD,2BAAqB,GAA5B,UAA6B,IAAa,EAAE,GAAW;QAGnD,IAAI,CAAC,IAAI,EAAE;YAEP,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SAC3C;QAED,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO;SACV;QAID,eAAe;QACf,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK;QACvB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC3E,GAAG,GAAG,MAAM,CAAC;SAChB;aAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,GAAG,MAAM,CAAC;SAChB;QAID,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,UAAC,GAAG,EAAE,OAAqB;YACrE,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,KAAK,CAAC,mDAAa,GAAG,CAAC,OAAO,IAAI,GAAG,CAAE,CAAC,CAAC;gBACjD,OAAO,CAAC,KAAK,CAAC,mCAAa,GAAK,CAAC,CAAC;gBAElC,kBAAkB;gBAClB,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO;aACV;YAID,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAE,UAAU;YAC9C,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAA,iBAAiB;YAC1C,QAAQ,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEnD,SAAS;YACT,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QAGvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,SAAS;IACF,wBAAkB,GAAzB,UAA0B,MAAiB;QAGvC,cAAc;QACd,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK;QAEvC,OAAO,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAGrD,CAAC;IAED,gBAAgB;IACT,gCAA0B,GAAjC,UAAkC,IAAa,EAAE,GAAW,EAAE,UAAuC;QAGjG,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAClC,IAAI,UAAU;gBAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YAClC,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SAC3C;QAED,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,IAAI,UAAU;gBAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YAClC,OAAO;SACV;QAED,eAAe;QACf,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK;QACvB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC3E,GAAG,GAAG,MAAM,CAAC;SAChB;aAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,GAAG,MAAM,CAAC;SAChB;QAID,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,UAAC,GAAG,EAAE,OAAqB;YACrE,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,KAAK,CAAC,mDAAa,GAAG,CAAC,OAAO,IAAI,GAAG,CAAE,CAAC,CAAC;gBACjD,OAAO,CAAC,KAAK,CAAC,mCAAa,GAAK,CAAC,CAAC;gBAElC,kBAAkB;gBAClB,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,UAAU;oBAAE,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClC,OAAO;aACV;YAID,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAE,UAAU;YAC9C,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAA,iBAAiB;YAC1C,QAAQ,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEnD,SAAS;YACT,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;YAGnB,IAAI,UAAU;gBAAE,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,MAAM;IACC,eAAS,GAAhB,UAAiB,IAAa,EAAE,KAAgB,EAAC,KAAa;QAC1D,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,eAAM,CAAC,YAAY,EAAE,eAAM,CAAC,aAAa,EAAE,eAAM,CAAC,iBAAiB,EAAE,eAAM,CAAC,kBAAkB,EAAE,KAAK,EAAC,KAAK,CAAC,CAAC;IACzI,CAAC;IACD,MAAM;IACC,iBAAW,GAAlB,UAAmB,IAAa,EAAE,KAAgB,EAAC,KAAa;QAC5D,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,eAAM,CAAC,cAAc,EAAE,eAAM,CAAC,eAAe,EAAE,eAAM,CAAC,mBAAmB,EAAE,eAAM,CAAC,oBAAoB,EAAE,KAAK,EAAC,KAAK,CAAC,CAAC;IACjJ,CAAC;IACD,MAAM;IACC,kBAAY,GAAnB,UAAoB,IAAa,EAAE,KAAgB,EAAC,KAAa;QAC7D,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,eAAM,CAAC,eAAe,EAAE,eAAM,CAAC,gBAAgB,EAAE,eAAM,CAAC,oBAAoB,EAAE,eAAM,CAAC,qBAAqB,EAAE,KAAK,EAAC,KAAK,CAAC,CAAC;IACrJ,CAAC;IACD,MAAM;IACC,gBAAU,GAAjB,UAAkB,IAAa,EAAE,KAAgB,EAAC,KAAa;QAC3D,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,eAAM,CAAC,aAAa,EAAE,eAAM,CAAC,aAAa,EAAE,eAAM,CAAC,kBAAkB,EAAE,eAAM,CAAC,kBAAkB,EAAE,KAAK,EAAC,KAAK,CAAC,CAAC;IAC3I,CAAC;IAGD,mBAAmB;IACZ,iBAAW,GAAlB,UAAmB,IAAa,EAAE,SAAiB,EAAE,UAAkB,EAAE,WAAmB,EAAE,YAAoB,EAAE,KAAgB,EAAC,SAAiB;QAElJ,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAA,UAAU;QACjE,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAA,UAAU;QAE7D,IAAI,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAG,SAAS,IAAE,IAAI,EAAC;YACf,KAAK,CAAC,MAAM,GAAG,SAAS,CAAA;SAC3B;QAED,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAC,IAAa;YACxC,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC3C,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,CAAC,UAAU,GAAE,EAAE,CAAA;YACpB,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YACtC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAC,SAAS,CAAC,CAAC;QAGhE,CAAC,EAAE,UAAC,IAAa;YACb,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1C,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,CAAC,UAAU,GAAE,EAAE,CAAA;YACpB,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACrC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAC,SAAS,CAAC,CAAC;YAC5D,IAAI,KAAK,IAAI,IAAI,EAAE;gBACf,KAAK,EAAE,CAAC;aACX;QACL,CAAC,EAAE,UAAC,IAAa;YACb,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1C,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,CAAC,UAAU,GAAE,EAAE,CAAA;YACpB,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;YAC3B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACrC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAC,SAAS,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,YAAY;IACL,sBAAgB,GAAvB,UAAwB,IAAa,EAAE,SAAiB,EAAE,UAAkB,EAAE,KAAe;QACzF,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,UAAC,IAAa;YACpC,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC,EAAE,UAAC,IAAa;YACb,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1C,KAAK,EAAE,CAAA;QACX,CAAC,EAAE,UAAC,IAAa;YACb,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,YAAY;IACL,eAAS,GAAhB,UAAiB,GAAW,EAAE,KAAiB,EAAE,GAAmB;;QAAtC,sBAAA,EAAA,SAAiB;QAAE,oBAAA,EAAA,WAAmB;QAChE,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,MAAM,GAAG,GAAG,EAAE;YACd,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;SAC5B;QAED,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACtC,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACpF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,MAAM,IAAI,CAAC,EAAE;gBACb,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC5B,IAAI,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC5B,IAAI,MAAM,SAAG,MAAM,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;gBAC7B,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK,EAAE;oBACxB,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;qBACpC;oBACD,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;iBACvE;qBACI;oBACD,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAClE,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;iBAC7D;aACJ;SACJ;IACL,CAAC;IAEM,oCAA8B,GAArC;QACI,IAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,IAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QACvC,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAClE,IAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3D,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9D,IAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAClE,IAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAClE,IAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAE5E,OAAU,IAAI,SAAI,KAAK,SAAI,GAAG,SAAI,KAAK,SAAI,OAAO,SAAI,OAAO,SAAI,YAAc,CAAC;IACpF,CAAC;IAED,UAAU;IACH,qBAAe,GAAtB,UAAuB,IAAY;QAC/B,IAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACpD,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;QAClC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC7B,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACpC,QAAQ,CAAC,MAAM,EAAE,CAAC;QAClB,IAAI;YACA,IAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,UAAU,EAAE;gBACZ,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aAC5B;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;aAC7B;SACJ;QAAC,OAAO,GAAG,EAAE;YACV,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;SACnC;QACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,4DAA4D;IACrD,gBAAU,GAAjB,UAAqB,GAAQ,EAAE,SAAiB;QAC5C,IAAM,MAAM,GAAU,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;YAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;SAC5C;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IACD,aAAa;IACN,2BAAqB,GAA5B,UAA6B,UAAmB;QAC5C,IAAI,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QACnD,IAAI,OAAO,GAAG,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAA;QAC3D,wCAAwC;QACxC,mEAAmE;QACnE,IAAI,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QAC7D,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;IAChC,CAAC;IAEL,YAAC;AAAD,CAnVA,AAmVC,IAAA;AAnVY,sBAAK", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { AudioManager } from \"./AudioManager\";\nimport { Config } from \"./Config\";\n\nconst { ccclass, property } = cc._decorator;\n\n\nexport class Tools {\n\n    static setTouchEvent(peopleNode: cc.Node, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {\n        this.setTouchEventParent(peopleNode, true, startFunction, endFunction, cancelFunction)\n    }\n    static setGameTouchEvent(peopleNode: cc.Node, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {\n        this.setTouchEventParent(peopleNode, false, startFunction, endFunction, cancelFunction)\n    }\n\n    //添加点击事件 \n    //isSound 是否需要按键音效，大厅的都需要 游戏内有自己的按键音所以不需要\n    //peopleNode 节点\n    //startFunction 按下事件\n    //endFunction 抬起事件\n    //cancelFunction 取消事件\n    private static setTouchEventParent(peopleNode: cc.Node, isSound: boolean, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {\n        peopleNode.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {\n            if (isSound) {\n                AudioManager.keyingToneAudio();\n            }\n\n            if (startFunction != null) {\n                startFunction(peopleNode, event);\n            }\n        }, this);\n        peopleNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n            if (endFunction != null) {\n                endFunction(peopleNode, event);\n            }\n        }, this);\n        peopleNode.on(cc.Node.EventType.TOUCH_CANCEL, (event: cc.Event.EventTouch) => {\n            if (cancelFunction != null) {\n                cancelFunction(peopleNode, event);\n            }\n        }, this);\n    }\n\n    static cancelTouchStartListener(peopleNode: cc.Node) {\n        peopleNode.off(cc.Node.EventType.TOUCH_START, this);\n    }\n    static cancelTouchEndListener(peopleNode: cc.Node) {\n        peopleNode.off(cc.Node.EventType.TOUCH_END, this);\n    }\n    static cancelTouchCancelListener(peopleNode: cc.Node) {\n        peopleNode.off(cc.Node.EventType.TOUCH_CANCEL, this);\n    }\n\n    //为精灵添加图片\n    static setNodeSpriteFrame(node: cc.Node, path: string) {\n        cc.resources.load(path, cc.SpriteFrame, function (error: Error, assets: cc.SpriteFrame) {\n            const sprite = node.getComponent(cc.Sprite);\n            sprite.spriteFrame = assets;\n        });\n    }\n\n    //添加网络图片\n    static setNodeSpriteFrameUrl(node: cc.Node, url: string) {\n    \n\n        if (!node) {\n\n            return;\n        }\n\n        let avatarSp = node.getComponent(cc.Sprite);\n        if (!avatarSp) {\n            console.warn(\"⚠️ 节点没有Sprite组件，正在添加...\");\n            avatarSp = node.addComponent(cc.Sprite);\n        }\n\n        if (url == null || url == '') {\n            console.warn(\"⚠️ URL为空，跳过图片加载\");\n            return;\n        }\n\n      \n\n        // 根据URL判断文件扩展名\n        let ext = '.png'; // 默认\n        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {\n            ext = '.jpg';\n        } else if (url.toLowerCase().includes('.png')) {\n            ext = '.png';\n        }\n\n       \n\n        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {\n            if (err) {\n                console.error(`❌ 图片加载失败: ${err.message || err}`);\n                console.error(`❌ 失败的URL: ${url}`);\n\n                // 尝试加载备用图片或设置默认颜色\n                Tools.setFallbackTexture(avatarSp);\n                return;\n            }\n\n            \n\n            texture.setPremultiplyAlpha(true);  // 👈 关键设置\n            texture.packable = false;//加载圆头像的时候 必须关闭合图\n            avatarSp.spriteFrame = new cc.SpriteFrame(texture);\n\n            // 确保节点可见\n            node.active = true;\n            node.opacity = 255;\n\n           \n        });\n    }\n\n    // 设置备用纹理\n    static setFallbackTexture(sprite: cc.Sprite) {\n       \n\n        // 创建一个简单的纯色纹理\n        let texture = new cc.Texture2D();\n        let color = [150, 150, 150, 255]; // 灰色\n\n        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);\n        sprite.spriteFrame = new cc.SpriteFrame(texture);\n\n      \n    }\n\n    // 异步加载网络图片（带回调）\n    static setNodeSpriteFrameUrlAsync(node: cc.Node, url: string, onComplete?: (success: boolean) => void) {\n       \n\n        if (!node) {\n            console.error(\"❌ 节点为null，无法设置图片\");\n            if (onComplete) onComplete(false);\n            return;\n        }\n\n        let avatarSp = node.getComponent(cc.Sprite);\n        if (!avatarSp) {\n            console.warn(\"⚠️ 节点没有Sprite组件，正在添加...\");\n            avatarSp = node.addComponent(cc.Sprite);\n        }\n\n        if (url == null || url == '') {\n            console.warn(\"⚠️ URL为空，跳过图片加载\");\n            if (onComplete) onComplete(false);\n            return;\n        }\n\n        // 根据URL判断文件扩展名\n        let ext = '.png'; // 默认\n        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {\n            ext = '.jpg';\n        } else if (url.toLowerCase().includes('.png')) {\n            ext = '.png';\n        }\n\n      \n\n        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {\n            if (err) {\n                console.error(`❌ 图片加载失败: ${err.message || err}`);\n                console.error(`❌ 失败的URL: ${url}`);\n\n                // 尝试加载备用图片或设置默认颜色\n                Tools.setFallbackTexture(avatarSp);\n                if (onComplete) onComplete(false);\n                return;\n            }\n\n          \n\n            texture.setPremultiplyAlpha(true);  // 👈 关键设置\n            texture.packable = false;//加载圆头像的时候 必须关闭合图\n            avatarSp.spriteFrame = new cc.SpriteFrame(texture);\n\n            // 确保节点可见\n            node.active = true;\n            node.opacity = 255;\n\n            \n            if (onComplete) onComplete(true);\n        });\n    }\n\n    //红色按钮\n    static redButton(node: cc.Node, click?: Function,label?:string) {\n        Tools.buttonState(node, Config.btnRedNormal, Config.btnRedPressed, Config.btnRedNormalColor, Config.btnRedPressedColor, click,label);\n    }\n    //绿色按钮\n    static greenButton(node: cc.Node, click?: Function,label?:string) {\n        Tools.buttonState(node, Config.btnGreenNormal, Config.btnGreenPressed, Config.btnGreenNormalColor, Config.btnGreenPressedColor, click,label);\n    }\n    //黄色按钮\n    static yellowButton(node: cc.Node, click?: Function,label?:string) {\n        Tools.buttonState(node, Config.btnYellowNormal, Config.btnYellowPressed, Config.btnYellowNormalColor, Config.btnYellowPressedColor, click,label);\n    }\n    //灰色按钮\n    static grayButton(node: cc.Node, click?: Function,label?:string) {\n        Tools.buttonState(node, Config.btnGrayNormal, Config.btnGrayNormal, Config.btnGrayNormalColor, Config.btnGrayNormalColor, click,label);\n    }\n\n\n    //通用的按钮点击事件，带点击变颜色的\n    static buttonState(node: cc.Node, normalImg: string, pressedImg: string, normalColor: string, pressedColor: string, click?: Function,labelText?:string) {\n\n        let btnGreen = node.getChildByName('btn_color_normal');//获取按钮背景节点\n        let btnLabel = node.getChildByName('button_label');//获取按钮文字节点\n\n        let label = btnLabel.getComponent(cc.Label);\n        let labelOutline = btnLabel.getComponent(cc.LabelOutline);\n\n        if(labelText!=null){\n            label.string = labelText\n        }\n\n        Tools.setTouchEvent(btnGreen, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, pressedImg);\n            label.fontSize = 34;\n            label.lineHeight= 34\n            let color = new cc.Color();\n            cc.Color.fromHEX(color, pressedColor);\n            labelOutline.color = color;\n            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#B3B3B3');\n\n\n        }, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, normalImg);\n            label.fontSize = 36;\n            label.lineHeight= 36\n            let color = new cc.Color();\n            cc.Color.fromHEX(color, normalColor);\n            labelOutline.color = color;\n            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#FFFFFF');\n            if (click != null) {\n                click();\n            }\n        }, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, normalImg);\n            label.fontSize = 36;\n            label.lineHeight= 36\n            let color = new cc.Color();\n            cc.Color.fromHEX(color, normalColor);\n            labelOutline.color = color;\n            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#FFFFFF');\n        });\n    }\n\n    //点击变颜色的图片按钮\n    static imageButtonClick(node: cc.Node, normalImg: string, pressedImg: string, click: Function) {\n        Tools.setTouchEvent(node, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, pressedImg);\n        }, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, normalImg);\n            click()\n        }, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, normalImg);\n        });\n    }\n\n    //格式化资金显示格式的\n    static NumToTBMK(num: number, digit: number = 1, min: number = 10000): string {\n        let intNum = num;\n        if (intNum < min) {\n            return intNum.toString();\n        }\n\n        let unitStrArr = [\"T\", \"B\", \"M\", \"K\"];\n        let unitArr = [Math.pow(10, 12), Math.pow(10, 9), Math.pow(10, 6), Math.pow(10, 3)];\n        for (let i = 0; i < unitArr.length; ++i) {\n            let result = intNum / unitArr[i];\n            if (result >= 1) {\n                let str = result.toString();\n                let strArr = str.split(\".\");\n                let suffix = strArr[1] ?? \"\";\n                if (suffix.length >= digit) {\n                    if (digit == 0) {\n                        return strArr[0] + unitStrArr[i];\n                    }\n                    return strArr[0] + \".\" + suffix.substring(0, digit) + unitStrArr[i];\n                }\n                else {\n                    let fillStr = new Array(digit - suffix.length).fill(\"0\").join(\"\");\n                    return strArr[0] + \".\" + suffix + fillStr + unitStrArr[i];\n                }\n            }\n        }\n    }\n\n    static getCurrentTimeWithMilliseconds(): string {\n        const currentDate = new Date();\n        const year = currentDate.getFullYear();\n        const month = String(currentDate.getMonth() + 1).padStart(2, '0');\n        const day = String(currentDate.getDate()).padStart(2, '0');\n        const hours = String(currentDate.getHours()).padStart(2, '0');\n        const minutes = String(currentDate.getMinutes()).padStart(2, '0');\n        const seconds = String(currentDate.getSeconds()).padStart(2, '0');\n        const milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0');\n\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;\n    }\n\n    //赋值文本到剪切板\n    static copyToClipboard(text: string) {\n        const textarea = document.createElement('textarea');\n        textarea.style.position = 'fixed';\n        textarea.style.opacity = '0';\n        textarea.value = text;\n        document.body.appendChild(textarea);\n        textarea.select();\n        try {\n            const successful = document.execCommand('copy');\n            if (successful) {\n                console.log('文本已复制到剪切板');\n            } else {\n                console.error('复制到剪切板失败');\n            }\n        } catch (err) {\n            console.error('复制到剪切板失败：', err);\n        }\n        document.body.removeChild(textarea);\n    }\n\n    //拆分数组用的，一个长度为 10 的数组 拆分成 2 个长度为 5 的数组 chunkArray(user_s, 5)\n    static chunkArray<T>(arr: T[], chunkSize: number): T[][] {\n        const result: T[][] = [];\n        for (let i = 0; i < arr.length; i += chunkSize) {\n            result.push(arr.slice(i, i + chunkSize));\n        }\n        return result;\n    }\n    //设置倒计时的秒数的位置\n    static setCountDownTimeLabel(buttonNode: cc.Node) {\n        let btn = buttonNode.getChildByName('button_label')\n        let timeBtn = buttonNode.getChildByName('buttonLabel_time')\n        // buttonLabel_time 紧贴着 button_label 的右边\n        // button_label的右边界 + buttonLabel_time宽度的一半 = buttonLabel_time的中心位置\n        let xPos = btn.position.x + btn.width / 2 + timeBtn.width / 2\n        timeBtn.setPosition(xPos, 0)\n    }\n\n}\n"]}