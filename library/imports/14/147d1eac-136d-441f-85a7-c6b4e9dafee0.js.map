{"version": 3, "sources": ["assets/scripts/game/Chess/HexSingleChessBoardController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,+DAA8D;AAC9D,iDAAgD;AAE1C,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAY1C;IAA2D,iDAAY;IAAvE;QAAA,qEAugDC;QApgDG,gBAAU,GAAc,IAAI,CAAC,CAAE,UAAU;QAGzC,kBAAY,GAAc,IAAI,CAAC,CAAE,YAAY;QAG7C,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAEzC,YAAY;QAEZ,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAG3C,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAG3C,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAG3C,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAG3C,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAG3C,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAE3C,YAAY;QACJ,sBAAgB,GAAY,IAAI,CAAC;QACjC,sBAAgB,GAAW,WAAW,CAAC,CAAE,eAAe;QAEhE,UAAU;QACO,cAAQ,GAAG,EAAE,CAAC,CAAE,QAAQ;QACxB,eAAS,GAAG,KAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAE,QAAQ;QACxC,gBAAU,GAAG,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAE,QAAQ;QAErE,wBAAwB;QAChB,iBAAW,GAAmC,IAAI,GAAG,EAAE,CAAC,CAAE,YAAY;QACtE,kBAAY,GAAyB,IAAI,GAAG,EAAE,CAAC,CAAE,YAAY;QAC7D,oBAAc,GAAe,EAAE,CAAC,CAAE,aAAa;QAEvD,SAAS;QACD,qBAAe,GAAY,KAAK,CAAC;QAEzC,UAAU;QACF,mBAAa,GAAW,CAAC,CAAC;QAC1B,uBAAiB,GAAW,EAAE,CAAC;QACtB,oBAAc,GAAG,GAAG,CAAC,CAAC,YAAY;QAEnD,SAAS;QACD,kBAAY,GAAW,GAAG,CAAC,CAAC,SAAS;QACrC,oBAAc,GAAY,IAAI,CAAC,CAAC,eAAe;;IA47C3D,CAAC;IA17CG,8CAAM,GAAN;QACI,2BAA2B;IAC/B,CAAC;IAED,6CAAK,GAAL;QACI,mCAAmC;QACnC,wBAAwB;IAC5B,CAAC;IAED;;;OAGG;IACK,0DAAkB,GAA1B,UAA2B,SAAiB;QACxC,QAAQ,SAAS,EAAE;YACf,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B;gBACI,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAED;;;OAGG;IACI,iDAAS,GAAhB,UAAiB,SAAiB;QAAlC,iBA+BC;QA9BG,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,iGAAoB,SAAW,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,iBAAiB;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,SAAS;QAElC,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,WAAW;QACX,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAE,cAAc;YAC3C,YAAY;YACZ,KAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC,CAAC;IAGZ,CAAC;IAED;;;OAGG;IACI,yDAAiB,GAAxB,UAAyB,OAAmB;QACxC,yBAAyB;QACzB,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,mEAA2B,GAAnC;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACpC,OAAO;SACV;QAED,IAAM,WAAW,GAAe,EAAE,CAAC;QACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gCAEvC,CAAC;YACN,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAE5B,sBAAsB;YACtB,IAAI,OAAK,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;;aAExC;YAED,IAAM,MAAM,GAAG,OAAK,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAEzD,IAAI,MAAM,EAAE;gBACR,gBAAgB;gBAChB,IAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAApC,CAAoC,CAAC,CAAC;gBAC3E,IAAI,CAAC,MAAM,EAAE;oBACT,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;iBAClD;aACJ;;;QAjBL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA/B,CAAC;SAkBT;QAED,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;IACtC,CAAC;IAED,WAAW;IACH,oDAAY,GAApB;QACI,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAE1B,aAAa;QACb,KAAoB,UAAmB,EAAnB,KAAA,IAAI,CAAC,cAAc,EAAnB,cAAmB,EAAnB,IAAmB,EAAE;YAApC,IAAM,KAAK,SAAA;YACZ,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE;gBACtB,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBACpD,SAAS,EAAE,KAAK;aACnB,CAAC,CAAC;SACN;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED,cAAc;IACN,iDAAS,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAClC,OAAU,CAAC,SAAI,CAAG,CAAC;IACvB,CAAC;IAED,cAAc;IACN,0DAAkB,GAA1B;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACN,mEAA2B,GAAnC;QACI,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAGD,eAAe;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAC9C,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACxB,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAI5B,sBAAsB;YACtB,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAErC,YAAY,EAAE,CAAC;gBACf,SAAS;aACZ;YAED,iBAAiB;YACjB,IAAI,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YACvD,IAAI,MAAM,EAAE;gBAER,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxD,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAClC,cAAc,EAAE,CAAC;aACpB;iBAAM;gBAEH,oBAAoB;gBACpB,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,QAAM,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC;gBACpD,IAAI,QAAM,EAAE;oBAER,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBACxD,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBAClC,cAAc,EAAE,CAAC;iBACpB;qBAAM;iBAEN;aACJ;SACJ;QAID,IAAI,cAAc,KAAK,CAAC,EAAE;YACtB,OAAO,CAAC,IAAI,CAAC,qHAAsB,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,uGAAiC,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,6DAAmD,CAAC,CAAC;SACrE;IACL,CAAC;IAED,eAAe;IACP,kEAA0B,GAAlC,UAAmC,QAAgB;QAC/C,IAAM,QAAQ,GAAG;YACb,4BAA4B;SAC/B,CAAC;QAEF,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;YAA3B,IAAM,OAAO,iBAAA;YACd,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACtC,IAAI,KAAK,EAAE;gBACP,IAAM,MAAM,GAAG,EAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;gBAC9D,OAAO,MAAM,CAAC;aACjB;SACJ;QAED,OAAO,CAAC,IAAI,CAAC,8DAAe,QAAU,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,uBAAuB;IACf,qDAAa,GAArB,UAAsB,KAAc,EAAE,QAAgB;QAClD,YAAY;QACZ,IAAI,QAAQ,KAAK,iBAAiB,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACjE,OAAO,IAAI,CAAC;SACf;QAED,cAAc;QACd,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC/E,OAAO,IAAI,CAAC;SACf;QAED,gBAAgB;QAChB,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACrF,OAAO,IAAI,CAAC;SACf;QAED,UAAU;QACV,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACxD,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,YAAY;IACJ,kEAA0B,GAAlC;QAAA,iBAuEC;QApEG,aAAa;QACb,IAAM,UAAU,GAAG;YACf,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;YAChE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE;YAC9D,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE;YAC5D,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE;YAC9D,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;SACjE,CAAC;QAGF,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,UAAU,CAAC,OAAO,CAAC,UAAA,KAAK;YACpB,IAAM,UAAU,GAAG,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzD,IAAM,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU;YAEtD,IAAI,SAAS,EAAE;gBACX,YAAY,EAAE,CAAC;aAElB;iBAAM;aAEN;QACL,CAAC,CAAC,CAAC;QAIH,qBAAqB;QACrB,IAAI,YAAY,GAAG,UAAU,CAAC,MAAM,EAAE;YAGlC,WAAW;YACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAM,IAAI,GAAG,UAAU,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC;gBAC7B,IAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACjD,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aAGpD;SACJ;QAED,cAAc;QAEd,IAAM,WAAW,GAAG;YAChB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;YACnC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;YACpC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;YACnC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE;YACpC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE;SACrD,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;YACrB,IAAM,UAAU,GAAG,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAElE,CAAC,CAAC,CAAC;QAEH,cAAc;QAEd,IAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,IAAM,aAAa,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAE,YAAY;IAIxD,CAAC;IAED,aAAa;IACL,oEAA4B,GAApC,UAAqC,GAAY;QAC7C,6BAA6B;QAC7B,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;QACpD,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAChF,OAAO,EAAC,CAAC,GAAA,EAAE,CAAC,GAAA,EAAC,CAAC;IAClB,CAAC;IAED,kCAAkC;IAC1B,2DAAmB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,cAA+B;QAA/B,+BAAA,EAAA,sBAA+B;QAG7E,eAAe;QACf,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,2CAAW,IAAI,CAAC,gBAAgB,wBAAM,CAAC,CAAC;YACtD,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACtB;QAED,cAAc;QACd,IAAM,GAAG,GAAM,CAAC,SAAI,CAAG,CAAC;QACxB,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAExC,oBAAoB;YACpB,IAAI,cAAc,EAAE;gBAChB,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;aACnC;YACD,OAAO,GAAG,CAAC;SACd;QAED,2BAA2B;QAC3B,IAAI,CAAS,EAAE,CAAS,CAAC;QAEzB,oBAAoB;QACpB,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YACvB,IAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC;YACxD,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;SAGd;aAAM;YACH,wBAAwB;YACxB,OAAO,CAAC,IAAI,CAAC,gCAAU,CAAC,mFAAe,CAAC,CAAC;YACzC,IAAM,MAAM,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;YACxC,IAAM,MAAM,GAAG,EAAE,CAAC;YAElB,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,GAAG,MAAM,CAAC;YACxD,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;SACjC;QAID,oBAAoB;QACpB,IAAI,cAAc,EAAE;YAChB,CAAC,IAAI,EAAE,CAAC;SACX;QAED,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC;IAED,6BAA6B;IACrB,sDAAc,GAAtB,UAAuB,SAAiB;QACpC,IAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QAE1B,wCAAwC;QACxC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG;YACxB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,IAAI,GAAG,CAAC;gBACjB,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACxB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;aAC3B,CAAC;YACF,OAAO,EAAE,IAAI,GAAG,CAAC;gBACb,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACrC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAQ,8BAA8B;aAC9E,CAAC;SACL,CAAC,CAAC;QAEH,gCAAgC;QAChC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG;YACrB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,IAAI,GAAG,CAAC;gBACjB,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;gBACvB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;aAC5B,CAAC;YACF,OAAO,EAAE,IAAI,GAAG,CAAC;gBACb,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACpC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAO,+BAA+B;aAC/E,CAAC;SACL,CAAC,CAAC;QAEH,gCAAgC;QAChC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG;YACxB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,IAAI,GAAG,CAAC;gBACjB,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;gBACvB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC7B,CAAC;YACF,OAAO,EAAE,IAAI,GAAG,CAAC;gBACb,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACpC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;gBACrC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAM,gCAAgC;aAChF,CAAC;SACL,CAAC,CAAC;QAEH,qCAAqC;QACrC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG;YACxB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,IAAI,GAAG,CAAC;gBACjB,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC7B,CAAC;YACF,OAAO,EAAE,IAAI,GAAG,CAAC;gBACb,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;aAC1C,CAAC;SACL,CAAC,CAAC;QAEH,8CAA8C;QAC9C,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG;YACxB,YAAY,EAAE,IAAI;YAClB,KAAK,EAAE,GAAG;YACV,WAAW,EAAE,IAAI,GAAG,CAAC;gBACjB,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACxB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC7B,CAAC;YACF,OAAO,EAAE,IAAI,GAAG,CAAC;gBACb,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACrC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;aAC1C,CAAC;SACL,CAAC,CAAC;QAEH,8CAA8C;QAC9C,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG;YACxB,YAAY,EAAE,EAAE;YAChB,KAAK,EAAE,GAAG;YACV,WAAW,EAAE,IAAI,GAAG,CAAC;gBACjB,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC7B,CAAC;YACF,OAAO,EAAE,IAAI,GAAG,CAAC;gBACb,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;aAC1C,CAAC;SACL,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED,eAAe;IACP,+DAAuB,GAA/B,UAAgC,QAAiB,EAAE,CAAS,EAAE,CAAS;QAAvE,iBA0BC;QAvBG,eAAe;QACf,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;YACnC,oBAAoB;YACpB,IAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,UAAU;SAE5D;QAED,eAAe;QACf,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE7C,YAAY;QACZ,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAEhE,KAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,YAAY;QACZ,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAG7C,CAAC;IAED,SAAS;IACD,2DAAmB,GAA3B,UAA4B,QAAiB,EAAE,CAAS,EAAE,CAAS;QAAnE,iBAyBC;QAxBG,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAM,mBAAmB,GAAG,GAAG,CAAC,CAAC,UAAU;QAE3C,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC5B,kBAAkB,GAAG,KAAK,CAAC;YAE3B,UAAU;YACV,KAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,IAAI,mBAAmB,EAAE;oBAC7E,kBAAkB,GAAG,IAAI,CAAC;oBAC1B,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACjC;YACL,CAAC,EAAE,mBAAmB,GAAG,IAAI,CAAC,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACrC,kBAAkB,GAAG,IAAI,CAAC,CAAC,WAAW;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YACxC,kBAAkB,GAAG,IAAI,CAAC,CAAC,WAAW;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,qBAAqB;IACb,sDAAc,GAAtB,UAAuB,CAAS,EAAE,CAAS,EAAE,MAA4B;QAGrE,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,+DAAgB,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,gBAAgB;QAChB,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,+BAAS,CAAC,UAAK,CAAC,oCAAQ,CAAC,CAAC;YACvC,OAAO;SACV;QAED,UAAU;QACV,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,IAAM,WAAW,GAAM,CAAC,SAAI,CAAG,CAAC;QAChC,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc;YACtD,IAAI,CAAC,iBAAiB,KAAK,WAAW,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAIrC,wCAAwC;QACxC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,qBAAqB;IACb,0DAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS;QAC3C,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,+DAAgB,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,sBAAsB;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACxB,2BAA2B;YAC3B,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1B,WAAW;YACX,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACrC;aAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YACzC,8BAA8B;YAC9B,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,SAAS;YACT,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACrC;aAAM;YACH,6BAA6B;YAC7B,OAAO,CAAC,IAAI,CAAC,kBAAM,CAAC,UAAK,CAAC,8EAAe,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,cAAc;IACP,4DAAoB,GAA3B,UAA4B,CAAS,EAAE,CAAS;QAC5C,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,kCAAkC;IAC1B,2DAAmB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC5D,IAAM,OAAO,GAAG;YACZ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,MAAM,CAAE,sBAAsB;SACzC,CAAC;QAEF,gBAAgB;QAChB,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED,qBAAqB;IACb,mDAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS;QACpC,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC1D,OAAO,KAAK,CAAC;SAChB;QAED,kBAAkB;QAClB,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC;IACpD,CAAC;IAED,mBAAmB;IACX,sDAAc,GAAtB,UAAuB,CAAS,EAAE,CAAS;QACvC,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU;YACrD,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE;YAE1C,SAAS;YACT,IAAM,YAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;YACvC,EAAE,CAAC,KAAK,CAAC,YAAU,CAAC;iBACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;iBAC7C,IAAI,CAAC;gBACF,YAAU,CAAC,gBAAgB,EAAE,CAAC;YAClC,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;YAEb,SAAS;YACT,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;SAC9B;IACL,CAAC;IAED,cAAc;IACP,0DAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAC1C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO;SACV;QAED,eAAe;QACf,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;QAE9B,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3C,cAAc;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAM,WAAW,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,KAAI,GAAG,CAAC;QAGzC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5E,KAAK,EAAE,CAAC;QAEb,SAAS;QACT,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;YAC1B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;SACpC;IACL,CAAC;IAED,YAAY;IACL,wDAAgB,GAAvB,UAAwB,CAAS,EAAE,CAAS,EAAE,aAA6B;QAA7B,8BAAA,EAAA,oBAA6B;QACvE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,aAAa;QACb,IAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC;QAE1B,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/B,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEzC,cAAc;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAM,WAAW,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,KAAI,GAAG,CAAC;QAGzC,SAAS;QACT,IAAM,WAAW,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,cAAc;QACrD,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5E,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;aACrD,KAAK,EAAE,CAAC;QAEb,sBAAsB;QACtB,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;QAED,SAAS;QACT,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;YAC1B,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC;SAClC;QAED,eAAe;QACf,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,UAAU;IACH,0DAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC1D,eAAe;QACf,IAAI,MAAM,GAAc,IAAI,CAAC;QAC7B,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC;gBACI,OAAO,CAAC,KAAK,CAAC,2CAAW,MAAQ,CAAC,CAAC;gBACnC,OAAO;SACd;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,SAAO,MAAM,sGAAwB,CAAC,CAAC;YACrD,OAAO;SACV;QAED,WAAW;QACX,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,YAAU,MAAQ,CAAC;QAErC,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3C,cAAc;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAM,WAAW,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,KAAI,GAAG,CAAC;QAGzC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5E,KAAK,EAAE,CAAC;QAEb,SAAS;QACT,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;YAC1B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;SACpC;IACL,CAAC;IAED,WAAW;IACH,+DAAuB,GAA/B;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO;SACV;QAED,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;QAC7D,IAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC;aAC1B,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC;aAC3E,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC;aAC3E,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC;aAC3E,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC;aAC3E,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC;aAC1D,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,uBAAuB;IAChB,qDAAa,GAApB,UAAqB,CAAS,EAAE,CAAS,EAAE,SAA0B;QAA1B,0BAAA,EAAA,iBAA0B;QAGjE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,mEAAe,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,oCAAc,OAAO,CAAC,YAAO,OAAO,CAAG,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,8CAAc,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,MAAI,CAAC,CAAC,CAAC,SAAI,CAAC,CAAC,CAAC,MAAG,EAAjB,CAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;YACzF,OAAO;SACV;QAED,SAAS;QACT,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,QAAQ,EAAE;YACV,IAAI,SAAS,EAAE;gBACX,aAAa;gBACb,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;aAC3B;iBAAM;gBACH,cAAc;gBACd,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;aAC3C;SACJ;IACL,CAAC;IAED,cAAc;IACN,gEAAwB,GAAhC,UAAiC,QAAiB;QAC9C,SAAS;QACT,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC3C,QAAQ,CAAC,kBAAkB,CAAC,GAAG,WAAW,CAAC;QAE3C,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,QAAQ,CACL,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EACpE,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAClC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CACrC;aACA,IAAI,CAAC;YACF,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,qBAAqB;IACd,0DAAkB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpC,OAAO;SACV;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAE5B,aAAa;QACb,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE1B,+BAA+B;YAC/B,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;gBACjE,UAAU,EAAE,CAAC;gBAEb,WAAW;gBACX,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBAE/F,IAAI,SAAS,EAAE;oBACX,aAAa,EAAE,CAAC;iBACnB;qBAAM;oBACH,mBAAmB,EAAE,CAAC;iBACzB;gBAED,gBAAgB;gBAChB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,SAAS;gBACT,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACpB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS;gBAE1B,kBAAkB;gBAClB,IAAI,KAAK,CAAC,kBAAkB,CAAC,EAAE;oBAC3B,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;iBAChD;gBAED,WAAW;gBACX,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;iBACzB;aACJ;SACJ;QAED,IAAI,UAAU,KAAK,CAAC,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;SAChD;IACL,CAAC;IAED,mBAAmB;IACZ,uDAAe,GAAtB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,gBAAgB;QAEzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAE5B,kBAAkB;YAClB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBAC7B,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBACzB,YAAY,EAAE,CAAC;aAClB;SACJ;QAED,gBAAgB;QAChB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpB,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;aAC9B;QACL,CAAC,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAED,uBAAuB;IACf,oDAAY,GAApB,UAAqB,QAAgB;QACjC,YAAY;QACZ,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,QAAQ,KAAK,UAAU,EAAE;YAC7D,OAAO,KAAK,CAAC;SAChB;QAED,QAAQ;QACR,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC;SACf;QAED,wCAAwC;QACxC,IAAI,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC;SACf;QAED,QAAQ;QACR,IAAI,QAAQ,KAAK,WAAW,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,2DAAmB,GAA1B,UAA2B,CAAS,EAAE,CAAS,EAAE,MAAW;QACxD,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,wEAAe,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACxB,aAAa;YACb,IAAM,UAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5D,IAAI,UAAQ,IAAI,UAAQ,CAAC,UAAU,EAAE;gBACjC,UAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBACvC,UAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;aAC9B;SACJ;QAED,kBAAkB;QAClB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;SAC7B;QAED,wBAAwB;QACxB,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,2DAAmB,GAA1B,UAA2B,aAAyB;QAApD,iBA2CC;QA1CG,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,OAAO;SACV;QAID,wBAAwB;QACxB,aAAa,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YAG/B,wBAAwB;YACxB,IAAI,MAAc,EAAE,MAAc,EAAE,aAAkB,CAAC;YAEvD,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;gBAChD,YAAY;gBACZ,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;gBACjB,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;gBACjB,aAAa,GAAG,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;aAE1F;iBAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;gBACvD,sBAAsB;gBACtB,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAE,SAAS;gBAC5B,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAE,SAAS;gBAC5B,aAAa,GAAG,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;aAE1F;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,4BAAU,KAAK,GAAG,CAAC,mDAAY,EAAE,KAAK,CAAC,CAAC;gBACtD,OAAO,CAAC,KAAK,CAAC,qCAAe,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;gBAC9D,OAAO;aACV;YAED,cAAc;YACd,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;gBACxD,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;gBAChC,OAAO,CAAC,KAAK,CAAC,4BAAU,KAAK,GAAG,CAAC,+DAAiB,MAAM,YAAO,MAAQ,CAAC,CAAC;gBACzE,OAAO;aACV;YAED,aAAa;YAEb,KAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;IACR,kEAA0B,GAAlC,UAAmC,CAAS,EAAE,CAAS,EAAE,MAAW;QAApE,iBAQC;QAPG,QAAQ;QACR,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAEhC,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAClD,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;;;;OAKG;IACI,kEAA0B,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,aAAkB;QACtE,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,MAAM,EAAE;YACtD,sBAAsB;YACtB,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,uBAAuB;YAC1D,eAAe;YACf,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC/B;aAAM,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,GAAG,CAAC,EAAE;YAC/D,OAAO;YACP,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;SAChD;aAAM;YACH,2BAA2B;SAC9B;IACL,CAAC;IAED;;OAEG;IACI,yDAAiB,GAAxB;QACI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,sDAAc,GAArB;QACI,kBAAkB;QAClB,0BAA0B;QAC1B,sCAAsC;IAC1C,CAAC;IAED;;OAEG;IACK,6DAAqB,GAA7B;QACI,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2DAAmB,GAA1B;QAGI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,GAAG;YACpC,IAAI,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAClC,cAAc;gBACd,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC1C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC5C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAE7C,oBAAoB;gBACpB,IAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAChD,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;iBAC1B;aACJ;QACL,CAAC,CAAC,CAAC;IAGP,CAAC;IAED;;;OAGG;IACI,iEAAyB,GAAhC;QACI,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,kDAAU,GAAjB;QAGI,YAAY;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAGjC,CAAC;IAED;;OAEG;IACI,0DAAkB,GAAzB;QAEI,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,0DAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS,EAAE,MAAiB,EAAE,QAAgB;QAC/E,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,6DAAc,QAAU,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,wDAAc,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC;SACf;QAED,SAAS;QACT,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;QAE3B,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAG3C,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,+DAAuB,GAA9B;QACI,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAEjC,CAAC;IAED;;OAEG;IACI,2DAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,6DAAqB,GAA5B;QACI,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,gBAAgB;YAChC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;SACxC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,uDAAe,GAAtB;QACI,IAAI,IAAI,GAAG;YACP,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC/C,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACpF,mBAAmB,EAAE,KAAK;YAC1B,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB;YACrC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YACtC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACxC,eAAe,EAAE,IAAI,CAAC,eAAe;SACxC,CAAC;QAEF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,uDAAe,GAAtB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,uDAAe,GAAtB;QACI,YAAY;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,yDAAiB,GAAxB,UAAyB,OAAY;QAArC,iBAwDC;QAvDG,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC;QAE9D,SAAS;QACT,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACxC,WAAW;YACX,IAAI,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;gBACjE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC1D,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,KAAU;oBACtC,0BAA0B;oBAC1B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBACpD,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBACpD,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;oBAE1C,IAAI,KAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBACjC,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,2CAAa,aAAe,CAAC,CAAC;wBAE9D,uBAAuB;wBACvB,KAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;wBAE/B,gCAAgC;wBAChC,IAAI,aAAa,GAAG,CAAC,EAAE;4BACnB,oBAAoB;4BACpB,KAAI,CAAC,YAAY,CAAC;gCACd,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,yBAAU,aAAe,CAAC,CAAC;gCAC3D,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;4BACjD,CAAC,EAAE,IAAI,CAAC,CAAC;yBACZ;qBACJ;yBAAM;wBACH,OAAO,CAAC,IAAI,CAAC,yBAAQ,CAAC,UAAK,CAAC,4DAAY,CAAC,CAAC;wBAC1C,OAAO,CAAC,IAAI,CAAC,8CAAc,KAAI,CAAC,cAAc,CAAC,MAAQ,CAAC,CAAC;wBACzD,OAAO,CAAC,IAAI,CAAC,iCAAqB,KAAI,CAAC,WAAW,CAAC,IAAM,CAAC,CAAC;qBAC9D;gBACL,CAAC,CAAC,CAAC;aACN;YAED,WAAW;YACX,IAAI,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC7D,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACxD,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,KAAU;oBACpC,0BAA0B;oBAC1B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBACpD,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBAEpD,IAAI,KAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBACjC,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;wBACrC,YAAY;wBACZ,KAAI,CAAC,YAAY,CAAC;4BACd,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAClC,CAAC,EAAE,GAAG,CAAC,CAAC;qBACX;gBACL,CAAC,CAAC,CAAC;aACN;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACI,kEAA0B,GAAjC,UAAkC,SAAc;QAC5C,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAE5D,iBAAiB;QACjB,0BAA0B;QAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,kBAAkB;QAClB,IAAI,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YAC7D,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,UAAU,CAAC;YAC3C,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC/B,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB;QAED,mBAAmB;QACnB,IAAI,SAAS,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC7C;IACL,CAAC;IAED;;OAEG;IACI,kDAAU,GAAjB,UAAkB,KAAa;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACI,iDAAS,GAAhB;QACI,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SACrC;IACL,CAAC;IAED;;OAEG;IACI,uDAAe,GAAtB;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAngDD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;qEACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;uEACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACU;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wEACY;IAjDb,6BAA6B;QADjD,OAAO;OACa,6BAA6B,CAugDjD;IAAD,oCAAC;CAvgDD,AAugDC,CAvgD0D,EAAE,CAAC,SAAS,GAugDtE;kBAvgDoB,6BAA6B", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { HexCoord } from \"../../bean/GameBean\";\nimport { WebSocketManager } from \"../../net/WebSocketManager\";\nimport { MessageId } from \"../../net/MessageId\";\n\nconst {ccclass, property} = cc._decorator;\n\n// 六边形单机模式格子数据接口\nexport interface HexSingleGridData {\n    q: number;  // 六边形坐标系q坐标\n    r: number;  // 六边形坐标系r坐标\n    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置\n    hasPlayer: boolean;  // 是否已经放置了预制体\n    playerNode?: cc.Node;  // 放置的节点引用\n}\n\n@ccclass\nexport default class HexSingleChessBoardController extends cc.Component {\n\n    @property(cc.Prefab)\n    boomPrefab: cc.Prefab = null;  // boom预制体\n\n    @property(cc.Prefab)\n    biaojiPrefab: cc.Prefab = null;  // biaoji预制体\n\n    @property(cc.Prefab)\n    boom1Prefab: cc.Prefab = null;  // 数字1预制体\n\n    @property(cc.Prefab)\n    boom2Prefab: cc.Prefab = null;  // 数字2预制体\n\n    @property(cc.Prefab)\n    boom3Prefab: cc.Prefab = null;  // 数字3预制体\n\n    @property(cc.Prefab)\n    boom4Prefab: cc.Prefab = null;  // 数字4预制体\n\n    @property(cc.Prefab)\n    boom5Prefab: cc.Prefab = null;  // 数字5预制体\n\n    @property(cc.Prefab)\n    boom6Prefab: cc.Prefab = null;  // 数字6预制体\n\n    @property(cc.Prefab)\n    boom7Prefab: cc.Prefab = null;  // 数字7预制体\n\n    @property(cc.Prefab)\n    boom8Prefab: cc.Prefab = null;  // 数字8预制体\n\n    // 六个六边形棋盘节点\n    @property(cc.Node)\n    hexBoard1Node: cc.Node = null;  // 六边形棋盘1节点\n\n    @property(cc.Node)\n    hexBoard2Node: cc.Node = null;  // 六边形棋盘2节点\n\n    @property(cc.Node)\n    hexBoard3Node: cc.Node = null;  // 六边形棋盘3节点\n\n    @property(cc.Node)\n    hexBoard4Node: cc.Node = null;  // 六边形棋盘4节点\n\n    @property(cc.Node)\n    hexBoard5Node: cc.Node = null;  // 六边形棋盘5节点\n\n    @property(cc.Node)\n    hexBoard6Node: cc.Node = null;  // 六边形棋盘6节点\n\n    // 当前使用的棋盘节点\n    private currentBoardNode: cc.Node = null;\n    private currentBoardType: string = \"hexBoard1\";  // 默认使用第一个六边形棋盘\n\n    // 六边形棋盘配置\n    private readonly HEX_SIZE = 44;  // 六边形半径\n    private readonly HEX_WIDTH = this.HEX_SIZE * 2;  // 六边形宽度\n    private readonly HEX_HEIGHT = this.HEX_SIZE * Math.sqrt(3);  // 六边形高度\n\n    // 格子数据存储 - 使用Map存储六边形坐标\n    private hexGridData: Map<string, HexSingleGridData> = new Map();  // 存储六边形格子数据\n    private hexGridNodes: Map<string, cc.Node> = new Map();  // 存储六边形格子节点\n    private validHexCoords: HexCoord[] = [];  // 有效的六边形坐标列表\n\n    // 炸弹爆炸标记\n    private hasBombExploded: boolean = false;\n\n    // 防重复发送消息\n    private lastClickTime: number = 0;\n    private lastClickPosition: string = \"\";\n    private readonly CLICK_COOLDOWN = 200; // 200毫秒冷却时间\n\n    // 缩放相关属性\n    private currentScale: number = 1.0; // 当前缩放比例\n    private zoomTargetNode: cc.Node = null; // 缩放目标节点（棋盘容器）\n\n    onLoad() {\n        // 不进行默认初始化，等待外部调用initBoard\n    }\n\n    start() {\n        // start方法不再自动启用触摸事件，避免与initBoard重复\n        // 触摸事件的启用由initBoard方法负责\n    }\n\n    /**\n     * 根据棋盘类型获取对应的棋盘节点\n     * @param boardType 棋盘类型\n     */\n    private getBoardNodeByType(boardType: string): cc.Node | null {\n        switch (boardType) {\n            case \"hexBoard1\":\n                return this.hexBoard1Node;\n            case \"hexBoard2\":\n                return this.hexBoard2Node;\n            case \"hexBoard3\":\n                return this.hexBoard3Node;\n            case \"hexBoard4\":\n                return this.hexBoard4Node;\n            case \"hexBoard5\":\n                return this.hexBoard5Node;\n            case \"hexBoard6\":\n                return this.hexBoard6Node;\n            default:\n                return null;\n        }\n    }\n\n    /**\n     * 初始化指定类型的六边形棋盘\n     * @param boardType 棋盘类型 (\"hexBoard1\", \"hexBoard2\", \"hexBoard3\", \"hexBoard4\", \"hexBoard5\", \"hexBoard6\")\n     */\n    public initBoard(boardType: string) {\n        // 根据棋盘类型获取对应的节点\n        this.currentBoardNode = this.getBoardNodeByType(boardType);\n        if (!this.currentBoardNode) {\n            console.error(`六边形棋盘节点未设置！棋盘类型: ${boardType}`);\n            return;\n        }\n\n        this.currentBoardType = boardType;\n\n        // 设置缩放目标节点（棋盘节点）\n        this.zoomTargetNode = this.currentBoardNode;\n        this.currentScale = 1.0; // 重置缩放比例\n\n        // 清空现有数据\n        this.hexGridData.clear();\n        this.hexGridNodes.clear();\n        this.validHexCoords = [];\n\n        // 重置炸弹爆炸标记\n        this.hasBombExploded = false;\n\n        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成\n        this.scheduleOnce(() => {\n            this.setValidHexCoords([]);  // 传入空数组，但会被忽略\n            // 测试预制体位置计算\n            this.testHexPositionCalculation();\n            this.enableTouchForExistingGrids();\n        }, 0.1);\n\n       \n    }\n\n    /**\n     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）\n     * @param _coords 服务器发送的坐标列表（将被忽略）\n     */\n    public setValidHexCoords(_coords: HexCoord[]) {\n        // 忽略服务器传入的坐标，始终从节点名称自动生成\n        this.generateCoordsFromNodeNames();\n        this.initHexBoard();\n    }\n\n    /**\n     * 从节点名称自动生成有效坐标列表\n     */\n    private generateCoordsFromNodeNames() {\n        if (!this.currentBoardNode) {\n            console.error(\"❌ 棋盘节点不存在，无法生成坐标列表\");\n            return;\n        }\n\n        const foundCoords: HexCoord[] = [];\n        const children = this.currentBoardNode.children;\n\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            const nodeName = child.name;\n\n            // 跳过游戏元素节点，只处理六边形格子节点\n            if (this.isGameElement(child, nodeName)) {\n                continue;\n            }\n\n            const coords = this.parseHexCoordinateFromName(nodeName);\n\n            if (coords) {\n                // 检查是否已经存在相同的坐标\n                const exists = foundCoords.some(c => c.q === coords.q && c.r === coords.r);\n                if (!exists) {\n                    foundCoords.push({ q: coords.q, r: coords.r });\n                }\n            }\n        }\n\n        this.validHexCoords = foundCoords;\n    }\n\n    // 初始化六边形棋盘\n    private initHexBoard() {\n        // 清空现有数据\n        this.hexGridData.clear();\n        this.hexGridNodes.clear();\n\n        // 初始化有效坐标的数据\n        for (const coord of this.validHexCoords) {\n            const key = this.getHexKey(coord.q, coord.r);\n            this.hexGridData.set(key, {\n                q: coord.q,\n                r: coord.r,\n                worldPos: this.getHexWorldPosition(coord.q, coord.r),\n                hasPlayer: false\n            });\n        }\n\n        this.createHexGridNodes();\n    }\n\n    // 生成六边形坐标的唯一键\n    private getHexKey(q: number, r: number): string {\n        return `${q},${r}`;\n    }\n\n    // 启用现有格子的触摸事件\n    private createHexGridNodes() {\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置！\");\n            return;\n        }\n\n        // 如果格子已经存在，直接启用触摸事件\n        this.enableTouchForExistingGrids();\n    }\n\n    // 为现有格子启用触摸事件\n    private enableTouchForExistingGrids() {\n        // 检查棋盘节点是否存在\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置，无法启用触摸事件！\");\n            return;\n        }\n\n\n        // 遍历棋盘节点的所有子节点\n        let children = this.currentBoardNode.children;\n        let validGridCount = 0;\n        let skippedCount = 0;\n\n        for (let i = 0; i < children.length; i++) {\n            let child = children[i];\n            const nodeName = child.name;\n\n           \n\n            // 跳过游戏元素节点，只处理六边形格子节点\n            if (this.isGameElement(child, nodeName)) {\n               \n                skippedCount++;\n                continue;\n            }\n\n            // 尝试从节点名称解析六边形坐标\n            let coords = this.parseHexCoordinateFromName(nodeName);\n            if (coords) {\n               \n                this.setupHexGridTouchEvents(child, coords.q, coords.r);\n                const key = this.getHexKey(coords.q, coords.r);\n                this.hexGridNodes.set(key, child);\n                validGridCount++;\n            } else {\n               \n                // 如果无法从名称解析，尝试从位置计算\n                let pos = child.getPosition();\n                let coords = this.getHexCoordinateFromPosition(pos);\n                if (coords) {\n                    \n                    this.setupHexGridTouchEvents(child, coords.q, coords.r);\n                    const key = this.getHexKey(coords.q, coords.r);\n                    this.hexGridNodes.set(key, child);\n                    validGridCount++;\n                } else {\n                   \n                }\n            }\n        }\n\n        \n\n        if (validGridCount === 0) {\n            console.warn(`⚠️ 没有找到任何有效的六边形格子节点！`);\n            console.warn(`   请检查格子节点命名是否为 sixblock_q_r 格式`);\n            console.warn(`   例如: sixblock_0_0, sixblock_1_-1, sixblock_-1_2`);\n        }\n    }\n\n    // 从节点名称解析六边形坐标\n    private parseHexCoordinateFromName(nodeName: string): {q: number, r: number} | null {\n        const patterns = [\n            /^sixblock_(-?\\d+)_(-?\\d+)$/,   // sixblock_q_r 格式\n        ];\n\n        for (const pattern of patterns) {\n            const match = nodeName.match(pattern);\n            if (match) {\n                const coords = {q: parseInt(match[1]), r: parseInt(match[2])};\n                return coords;\n            }\n        }\n\n        console.warn(`❌ 无法解析节点名称: ${nodeName}`);\n        return null;\n    }\n\n    // 判断是否为游戏元素节点（需要跳过的节点）\n    private isGameElement(child: cc.Node, nodeName: string): boolean {\n        // 跳过玩家头像预制体\n        if (nodeName === \"player_game_pfb\" || child.name.includes(\"Player\")) {\n            return true;\n        }\n\n        // 跳过boom相关预制体\n        if (nodeName === \"Boom\" || nodeName.includes(\"Boom\") || nodeName.includes(\"boom\")) {\n            return true;\n        }\n\n        // 跳过biaoji相关预制体\n        if (nodeName === \"Biaoji\" || nodeName.includes(\"Biaoji\") || nodeName.includes(\"biaoji\")) {\n            return true;\n        }\n\n        // 跳过数字预制体\n        if (nodeName.match(/^\\d+$/) || nodeName.includes(\"Number\")) {\n            return true;\n        }\n\n        return false;\n    }\n\n    // 测试六边形位置计算\n    private testHexPositionCalculation() {\n      \n\n        // 您提供的实际测量数据\n        const testPoints = [\n            { q: 0, r: 0, expected: cc.v2(-170, -165), description: \"中心位置\" },\n            { q: 0, r: -1, expected: cc.v2(-220, -81), description: \"上方\" },\n            { q: 1, r: -2, expected: cc.v2(-172, 2), description: \"右上\" },\n            { q: 2, r: -3, expected: cc.v2(-122, 85), description: \"右上远\" },\n            { q: 4, r: -4, expected: cc.v2(23, 171), description: \"右上最远\" }\n        ];\n\n      \n        let correctCount = 0;\n\n        testPoints.forEach(point => {\n            const calculated = this.getHexWorldPosition(point.q, point.r);\n            const errorX = Math.abs(calculated.x - point.expected.x);\n            const errorY = Math.abs(calculated.y - point.expected.y);\n            const isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差\n\n            if (isCorrect) {\n                correctCount++;\n                \n            } else {\n                \n            }\n        });\n\n\n\n        // 如果不是全部正确，显示详细的数据分析\n        if (correctCount < testPoints.length) {\n           \n\n            // 分析相邻点的差值\n            for (let i = 1; i < testPoints.length; i++) {\n                const prev = testPoints[i-1];\n                const curr = testPoints[i];\n                const deltaQ = curr.q - prev.q;\n                const deltaR = curr.r - prev.r;\n                const deltaX = curr.expected.x - prev.expected.x;\n                const deltaY = curr.expected.y - prev.expected.y;\n\n                \n            }\n        }\n\n        // 测试一些关键的推算坐标\n        \n        const extraPoints = [\n            { q: 1, r: 0, description: \"右侧邻居\" },\n            { q: -1, r: 0, description: \"左侧邻居\" },\n            { q: 0, r: 1, description: \"下方邻居\" },\n            { q: 1, r: -1, description: \"右上邻居\" },\n            { q: 2, r: -2, description: \"应该在(1,-2)和(2,-3)之间\" }\n        ];\n\n        extraPoints.forEach(point => {\n            const calculated = this.getHexWorldPosition(point.q, point.r);\n            \n        });\n\n        // 验证左右间距（q方向）\n       \n        const pos1 = this.getHexWorldPosition(0, 0);\n        const pos2 = this.getHexWorldPosition(1, 0);\n        const actualSpacing = pos2.x - pos1.x;  // 不用绝对值，看方向\n        \n\n      \n    }\n\n    // 从位置计算六边形坐标\n    private getHexCoordinateFromPosition(pos: cc.Vec2): {q: number, r: number} | null {\n        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法\n        const q = Math.round(pos.x / (this.HEX_SIZE * 1.5));\n        const r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);\n        return {q, r};\n    }\n\n    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）\n    private getHexWorldPosition(q: number, r: number, isPlayerAvatar: boolean = false): cc.Vec2 {\n       \n\n        // 根据当前棋盘类型获取配置\n        const config = this.getBoardConfig(this.currentBoardType);\n        if (!config) {\n            console.error(`❌ 未找到棋盘 ${this.currentBoardType} 的配置`);\n            return cc.v2(0, 0);\n        }\n\n        // 首先检查是否有精确坐标\n        const key = `${q},${r}`;\n        if (config.exactCoords.has(key)) {\n            const pos = config.exactCoords.get(key);\n            \n            // 如果是玩家头像预制体，y轴向上偏移\n            if (isPlayerAvatar) {\n                return cc.v2(pos.x, pos.y + 20);\n            }\n            return pos;\n        }\n\n        // 使用联机版的逻辑：每行有基准点，使用统一步长计算\n        let x: number, y: number;\n\n        // 如果有该行的数据，使用统一步长计算\n        if (config.rowData.has(r)) {\n            const data = config.rowData.get(r);\n            x = data.baseX + (q - data.baseQ) * config.uniformStepX;\n            y = data.y;\n\n           \n        } else {\n            // 对于其他行，使用通用的六边形轴线坐标系公式\n            console.warn(`⚠️ 没有r=${r}行的精确数据，使用通用公式`);\n            const stepXR = -config.uniformStepX / 2;\n            const stepYR = 74;\n\n            x = config.baseX + q * config.uniformStepX + r * stepXR;\n            y = config.baseY - r * stepYR;\n        }\n\n       \n\n        // 如果是玩家头像预制体，y轴向上偏移\n        if (isPlayerAvatar) {\n            y += 20;\n        }\n\n        return cc.v2(x, y);\n    }\n\n    // 获取棋盘配置（参考联机版Level_S001的实现）\n    private getBoardConfig(boardType: string) {\n        const configs = new Map();\n\n        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据\n        configs.set(\"hexBoard1\", {\n            baseX: -170, baseY: -165,\n            uniformStepX: 97,\n            exactCoords: new Map([\n                [\"0,0\", cc.v2(-170, -165)],\n                [\"0,-1\", cc.v2(-220, -81)],\n                [\"1,-2\", cc.v2(-172, 2)],\n                [\"2,-3\", cc.v2(-122, 85)],\n                [\"4,-4\", cc.v2(23, 171)]\n            ]),\n            rowData: new Map([\n                [0, { baseQ: 0, baseX: -170, y: -165 }],     // r=0行：基准点(0,0) → (-170, -165)\n                [-1, { baseQ: 0, baseX: -220, y: -81 }],     // r=-1行：基准点(0,-1) → (-220, -81)\n                [-2, { baseQ: 1, baseX: -172, y: 2 }],       // r=-2行：基准点(1,-2) → (-172, 2)\n                [-3, { baseQ: 2, baseX: -122, y: 85 }],      // r=-3行：基准点(2,-3) → (-122, 85)\n                [-4, { baseQ: 4, baseX: 23, y: 171 }]        // r=-4行：基准点(4,-4) → (23, 171)\n            ])\n        });\n\n        // Level_S002 (hexBoard2) - 第10关\n        configs.set(\"hexBoard2\", {\n            baseX: 0, baseY: -293,\n            uniformStepX: 98,\n            exactCoords: new Map([\n                [\"0,0\", cc.v2(0, -293)],\n                [\"0,-1\", cc.v2(-50, -209)],\n                [\"0,-2\", cc.v2(-100, -125)],\n                [\"0,-3\", cc.v2(-150, -42)],\n                [\"1,-4\", cc.v2(-100, 44)],\n                [\"2,-5\", cc.v2(-50, 127)],\n                [\"2,-6\", cc.v2(-100, 210)],\n                [\"3,-7\", cc.v2(-50, 293)]\n            ]),\n            rowData: new Map([\n                [0, { baseQ: 0, baseX: 0, y: -293 }],        // r=0行：基准点(0,0) → (0, -293)\n                [-1, { baseQ: 0, baseX: -50, y: -209 }],     // r=-1行：基准点(0,-1) → (-50, -209)\n                [-2, { baseQ: 0, baseX: -100, y: -125 }],    // r=-2行：基准点(0,-2) → (-100, -125)\n                [-3, { baseQ: 0, baseX: -150, y: -42 }],     // r=-3行：基准点(0,-3) → (-150, -42)\n                [-4, { baseQ: 1, baseX: -100, y: 44 }],      // r=-4行：基准点(1,-4) → (-100, 44)\n                [-5, { baseQ: 2, baseX: -50, y: 127 }],      // r=-5行：基准点(2,-5) → (-50, 127)\n                [-6, { baseQ: 2, baseX: -100, y: 210 }],     // r=-6行：基准点(2,-6) → (-100, 210)\n                [-7, { baseQ: 3, baseX: -50, y: 293 }]       // r=-7行：基准点(3,-7) → (-50, 293)\n            ])\n        });\n\n        // Level_S003 (hexBoard3) - 第15关\n        configs.set(\"hexBoard3\", {\n            baseX: -146, baseY: -250,\n            uniformStepX: 98,\n            exactCoords: new Map([\n                [\"0,0\", cc.v2(-146, -250)],\n                [\"1,1\", cc.v2(0, -336)],\n                [\"0,-1\", cc.v2(-196, -168)],\n                [\"1,-2\", cc.v2(-146, -85)],\n                [\"2,-3\", cc.v2(-99, -1)],      // 更新坐标：(-198, -1) → (-99, -1)\n                [\"1,-4\", cc.v2(-246, 84)],\n                [\"1,-5\", cc.v2(-293, 167)],\n                [\"2,-6\", cc.v2(-246, 251)],\n                [\"3,-7\", cc.v2(-196, 336)]\n            ]),\n            rowData: new Map([\n                [1, { baseQ: 1, baseX: 0, y: -336 }],        // r=1行：基准点(1,1) → (0, -336)\n                [0, { baseQ: 0, baseX: -146, y: -250 }],     // r=0行：基准点(0,0) → (-146, -250)\n                [-1, { baseQ: 0, baseX: -196, y: -168 }],    // r=-1行：基准点(0,-1) → (-196, -168)\n                [-2, { baseQ: 1, baseX: -146, y: -85 }],     // r=-2行：基准点(1,-2) → (-146, -85)\n                [-3, { baseQ: 2, baseX: -99, y: -1 }],       // r=-3行：基准点(2,-3) → (-99, -1) 更新\n                [-4, { baseQ: 1, baseX: -246, y: 84 }],      // r=-4行：基准点(1,-4) → (-246, 84)\n                [-5, { baseQ: 1, baseX: -293, y: 167 }],     // r=-5行：基准点(1,-5) → (-293, 167)\n                [-6, { baseQ: 2, baseX: -246, y: 251 }],     // r=-6行：基准点(2,-6) → (-246, 251)\n                [-7, { baseQ: 3, baseX: -196, y: 336 }]      // r=-7行：基准点(3,-7) → (-196, 336)\n            ])\n        });\n\n        // Level_S004 (hexBoard4) - 第20关，同联机版\n        configs.set(\"hexBoard4\", {\n            baseX: -300, baseY: -258,\n            uniformStepX: 86,\n            exactCoords: new Map([\n                [\"0,0\", cc.v2(-300, -258)],\n                [\"1,-1\", cc.v2(-258, -184)],\n                [\"1,-2\", cc.v2(-300, -108)],\n                [\"2,-3\", cc.v2(-258, -36)],\n                [\"2,-4\", cc.v2(-300, 37)],\n                [\"3,-5\", cc.v2(-258, 110)],\n                [\"3,-6\", cc.v2(-300, 185)],\n                [\"4,-7\", cc.v2(-258, 260)]\n            ]),\n            rowData: new Map([\n                [0, { baseQ: 0, baseX: -300, y: -258 }],\n                [-1, { baseQ: 1, baseX: -258, y: -184 }],\n                [-2, { baseQ: 1, baseX: -300, y: -108 }],\n                [-3, { baseQ: 2, baseX: -258, y: -36 }],\n                [-4, { baseQ: 2, baseX: -300, y: 37 }],\n                [-5, { baseQ: 3, baseX: -258, y: 110 }],\n                [-6, { baseQ: 3, baseX: -300, y: 185 }],\n                [-7, { baseQ: 4, baseX: -258, y: 260 }]\n            ])\n        });\n\n        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8\n        configs.set(\"hexBoard5\", {\n            baseX: -257, baseY: -293,\n            uniformStepX: 85.5,  // 左右间距86\n            scale: 0.8,  // 预制体缩放0.8\n            exactCoords: new Map([\n                [\"0,0\", cc.v2(-257, -293)],\n                [\"0,-1\", cc.v2(-300, -219)],\n                [\"1,-2\", cc.v2(-257, -146)],\n                [\"1,-3\", cc.v2(-300, -74)],\n                [\"2,-4\", cc.v2(-257, 0)],\n                [\"2,-5\", cc.v2(-300, 74)],\n                [\"3,-6\", cc.v2(-257, 146)],\n                [\"3,-7\", cc.v2(-300, 219)],\n                [\"4,-8\", cc.v2(-257, 293)]\n            ]),\n            rowData: new Map([\n                [0, { baseQ: 0, baseX: -257, y: -293 }],\n                [-1, { baseQ: 0, baseX: -300, y: -219 }],\n                [-2, { baseQ: 1, baseX: -257, y: -146 }],\n                [-3, { baseQ: 1, baseX: -300, y: -74 }],\n                [-4, { baseQ: 2, baseX: -257, y: 0 }],\n                [-5, { baseQ: 2, baseX: -300, y: 74 }],\n                [-6, { baseQ: 3, baseX: -257, y: 146 }],\n                [-7, { baseQ: 3, baseX: -300, y: 219 }],\n                [-8, { baseQ: 4, baseX: -257, y: 293 }]\n            ])\n        });\n\n        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8\n        configs.set(\"hexBoard6\", {\n            baseX: -313, baseY: -298,\n            uniformStepX: 78,\n            scale: 0.8,  // 预制体缩放0.8\n            exactCoords: new Map([\n                [\"0,0\", cc.v2(-313, -298)],\n                [\"1,-1\", cc.v2(-274, -233)],\n                [\"1,-2\", cc.v2(-313, -165)],\n                [\"2,-3\", cc.v2(-274, -99)],\n                [\"2,-4\", cc.v2(-313, -34)],\n                [\"3,-5\", cc.v2(-274, 34)],\n                [\"3,-6\", cc.v2(-313, 96)],\n                [\"4,-7\", cc.v2(-274, 165)],\n                [\"4,-8\", cc.v2(-313, 226)],\n                [\"5,-9\", cc.v2(-274, 300)]\n            ]),\n            rowData: new Map([\n                [0, { baseQ: 0, baseX: -313, y: -298 }],\n                [-1, { baseQ: 1, baseX: -274, y: -233 }],\n                [-2, { baseQ: 1, baseX: -313, y: -165 }],\n                [-3, { baseQ: 2, baseX: -274, y: -99 }],\n                [-4, { baseQ: 2, baseX: -313, y: -34 }],\n                [-5, { baseQ: 3, baseX: -274, y: 34 }],\n                [-6, { baseQ: 3, baseX: -313, y: 96 }],\n                [-7, { baseQ: 4, baseX: -274, y: 165 }],\n                [-8, { baseQ: 4, baseX: -313, y: 226 }],\n                [-9, { baseQ: 5, baseX: -274, y: 300 }]\n            ])\n        });\n\n        return configs.get(boardType);\n    }\n\n    // 为六边形格子设置触摸事件\n    private setupHexGridTouchEvents(gridNode: cc.Node, q: number, r: number) {\n       \n\n        // 确保节点可以接收触摸事件\n        if (!gridNode.getComponent(cc.Button)) {\n            // 如果没有Button组件，添加一个\n            const button = gridNode.addComponent(cc.Button);\n            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈\n          \n        }\n\n        // 移除现有的触摸事件监听器\n        gridNode.off(cc.Node.EventType.TOUCH_END);\n        gridNode.off(cc.Node.EventType.TOUCH_START);\n        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);\n\n        // 添加点击事件监听器\n        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n           \n            this.onHexGridClick(q, r, event);\n        }, this);\n\n        // 添加长按事件监听器\n        this.setupLongPressEvent(gridNode, q, r);\n\n        \n    }\n\n    // 设置长按事件\n    private setupLongPressEvent(gridNode: cc.Node, q: number, r: number) {\n        let touchStartTime = 0;\n        let longPressTriggered = false;\n        const LONG_PRESS_DURATION = 500; // 500毫秒长按\n\n        gridNode.on(cc.Node.EventType.TOUCH_START, () => {\n            touchStartTime = Date.now();\n            longPressTriggered = false;\n            \n            // 设置长按定时器\n            this.scheduleOnce(() => {\n                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {\n                    longPressTriggered = true;\n                    this.onHexGridLongPress(q, r);\n                }\n            }, LONG_PRESS_DURATION / 1000);\n        }, this);\n\n        gridNode.on(cc.Node.EventType.TOUCH_END, () => {\n            longPressTriggered = true; // 防止长按事件触发\n        }, this);\n\n        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, () => {\n            longPressTriggered = true; // 防止长按事件触发\n        }, this);\n    }\n\n    // 六边形格子点击事件 - 发送挖掘操作\n    private onHexGridClick(q: number, r: number, _event?: cc.Event.EventTouch) {\n       \n\n        // 检查坐标是否有效\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);\n            return;\n        }\n\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        // 检查该位置是否已经有预制体\n        if (gridData && gridData.hasPlayer) {\n            console.warn(`⚠️ 格子(${q}, ${r})已有预制体`);\n            return;\n        }\n\n        // 防重复点击检查\n        const currentTime = Date.now();\n        const positionKey = `${q},${r}`;\n        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&\n            this.lastClickPosition === positionKey) {\n            console.warn(\"点击过于频繁，忽略本次点击\");\n            return;\n        }\n\n        this.lastClickTime = currentTime;\n        this.lastClickPosition = positionKey;\n\n       \n\n        // 发送LevelClickBlock消息 (action = 1 表示挖掘)\n        this.sendLevelClickBlock(q, r, 1);\n    }\n\n    // 六边形格子长按事件 - 发送标记操作\n    private onHexGridLongPress(q: number, r: number) {\n        // 检查坐标是否有效\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);\n            return;\n        }\n\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        // 检查该位置是否已经有biaoji预制体\n        if (this.hasBiaojiAt(q, r)) {\n            // 如果已经有biaoji，则删除它（本地立即处理）\n            this.removeBiaojiAt(q, r);\n            // 发送取消标记消息\n            this.sendLevelClickBlock(q, r, 2);\n        } else if (!gridData || !gridData.hasPlayer) {\n            // 如果没有任何预制体，则生成biaoji（本地立即处理）\n            this.createBiaojiPrefab(q, r);\n            // 发送标记消息\n            this.sendLevelClickBlock(q, r, 2);\n        } else {\n            // 如果有其他类型的预制体（如数字、boom），则不处理\n            console.warn(`格子(${q}, ${r})已有其他预制体，无法标记`);\n        }\n    }\n\n    // 检查六边形坐标是否有效\n    public isValidHexCoordinate(q: number, r: number): boolean {\n        const key = this.getHexKey(q, r);\n        return this.hexGridData.has(key);\n    }\n\n    // 发送LevelClickBlock消息（参考四边形单机控制器）\n    private sendLevelClickBlock(q: number, r: number, action: number) {\n        const message = {\n            q: q,\n            r: r,\n            action: action  // 1 = 挖掘, 2 = 标记/取消标记\n        };\n\n        // 发送WebSocket消息\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLevelClickBlock, message);\n    }\n\n    // 检查指定位置是否有biaoji预制体\n    private hasBiaojiAt(q: number, r: number): boolean {\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {\n            return false;\n        }\n\n        // 检查节点名称是否为Biaoji\n        return gridData.playerNode.name === \"HexBiaoji\";\n    }\n\n    // 移除指定位置的biaoji预制体\n    private removeBiaojiAt(q: number, r: number) {\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        if (gridData && gridData.hasPlayer && gridData.playerNode &&\n            gridData.playerNode.name === \"HexBiaoji\") {\n\n            // 播放消失动画\n            const biaojiNode = gridData.playerNode;\n            cc.tween(biaojiNode)\n                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })\n                .call(() => {\n                    biaojiNode.removeFromParent();\n                })\n                .start();\n\n            // 更新格子数据\n            gridData.hasPlayer = false;\n            gridData.playerNode = null;\n        }\n    }\n\n    // 创建biaoji预制体\n    public createBiaojiPrefab(q: number, r: number) {\n        if (!this.biaojiPrefab) {\n            console.error(\"biaojiPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化biaoji预制体\n        const biaojiNode = cc.instantiate(this.biaojiPrefab);\n        biaojiNode.name = \"HexBiaoji\";\n\n        // 设置位置\n        const position = this.getHexWorldPosition(q, r, false);\n        biaojiNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(biaojiNode);\n\n        // 获取当前棋盘的缩放配置\n        const config = this.getBoardConfig(this.currentBoardType);\n        const targetScale = config?.scale || 1.0;\n       \n\n        // 播放出现动画\n        biaojiNode.setScale(0);\n        cc.tween(biaojiNode)\n            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })\n            .start();\n\n        // 更新格子数据\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        if (gridData) {\n            gridData.hasPlayer = true;\n            gridData.playerNode = biaojiNode;\n        }\n    }\n\n    // 创建boom预制体\n    public createBoomPrefab(q: number, r: number, isCurrentUser: boolean = true) {\n        if (!this.boomPrefab) {\n            console.error(\"boomPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化boom预制体\n        const boomNode = cc.instantiate(this.boomPrefab);\n        boomNode.name = \"HexBoom\";\n\n        // 设置位置\n        const position = this.getHexWorldPosition(q, r, false);\n        boomNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(boomNode);\n\n        // 获取当前棋盘的缩放配置\n        const config = this.getBoardConfig(this.currentBoardType);\n        const targetScale = config?.scale || 1.0;\n       \n\n        // 播放出现动画\n        const bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放\n        boomNode.setScale(0);\n        cc.tween(boomNode)\n            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })\n            .to(0.1, { scaleX: targetScale, scaleY: targetScale })\n            .start();\n\n        // 只有当前用户点到雷时才播放棋盘震动效果\n        if (isCurrentUser) {\n            this.playBoardShakeAnimation();\n        }\n\n        // 更新格子数据\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        if (gridData) {\n            gridData.hasPlayer = true;\n            gridData.playerNode = boomNode;\n        }\n\n        // 设置标记，表示点到了炸弹\n        this.hasBombExploded = true;\n    }\n\n    // 创建数字预制体\n    public createNumberPrefab(q: number, r: number, number: number) {\n        // 根据数字选择对应的预制体\n        let prefab: cc.Prefab = null;\n        switch (number) {\n            case 1: prefab = this.boom1Prefab; break;\n            case 2: prefab = this.boom2Prefab; break;\n            case 3: prefab = this.boom3Prefab; break;\n            case 4: prefab = this.boom4Prefab; break;\n            case 5: prefab = this.boom5Prefab; break;\n            case 6: prefab = this.boom6Prefab; break;\n            case 7: prefab = this.boom7Prefab; break;\n            case 8: prefab = this.boom8Prefab; break;\n            default:\n                console.error(`不支持的数字: ${number}`);\n                return;\n        }\n\n        if (!prefab) {\n            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);\n            return;\n        }\n\n        // 实例化数字预制体\n        const numberNode = cc.instantiate(prefab);\n        numberNode.name = `HexBoom${number}`;\n\n        // 设置位置\n        const position = this.getHexWorldPosition(q, r, false);\n        numberNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(numberNode);\n\n        // 获取当前棋盘的缩放配置\n        const config = this.getBoardConfig(this.currentBoardType);\n        const targetScale = config?.scale || 1.0;\n        \n\n        // 播放出现动画\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })\n            .start();\n\n        // 更新格子数据\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        if (gridData) {\n            gridData.hasPlayer = true;\n            gridData.playerNode = numberNode;\n        }\n    }\n\n    // 播放棋盘震动动画\n    private playBoardShakeAnimation() {\n        if (!this.currentBoardNode) {\n            return;\n        }\n\n        const originalPosition = this.currentBoardNode.getPosition();\n        const shakeIntensity = 10;\n\n        cc.tween(this.currentBoardNode)\n            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })\n            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })\n            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })\n            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })\n            .to(0.05, { x: originalPosition.x, y: originalPosition.y })\n            .start();\n    }\n\n    // 隐藏指定位置的六边形小格子（点击时调用）\n    public hideHexGridAt(q: number, r: number, immediate: boolean = false) {\n      \n\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`❌ 隐藏格子失败：坐标(${q}, ${r})无效`);\n            console.warn(`   坐标类型: q=${typeof q}, r=${typeof r}`);\n            console.warn(`   有效坐标列表: ${this.validHexCoords.map(c => `(${c.q},${c.r})`).join(', ')}`);\n            return;\n        }\n\n        // 获取格子节点\n        const key = this.getHexKey(q, r);\n        const gridNode = this.hexGridNodes.get(key);\n        if (gridNode) {\n            if (immediate) {\n                // 立即隐藏，不播放动画\n                gridNode.active = false;\n            } else {\n                // 播放六边形格子消失动画\n                this.playHexGridFallAnimation(gridNode);\n            }\n        }\n    }\n\n    // 播放六边形格子掉落动画\n    private playHexGridFallAnimation(gridNode: cc.Node) {\n        // 保存原始位置\n        const originalPos = gridNode.getPosition();\n        gridNode['originalPosition'] = originalPos;\n\n        // 播放掉落动画\n        cc.tween(gridNode)\n            .parallel(\n                cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }),\n                cc.tween().to(0.3, { opacity: 0 }),\n                cc.tween().to(0.5, { angle: 180 })\n            )\n            .call(() => {\n                gridNode.active = false;\n            })\n            .start();\n    }\n\n    // 显示所有隐藏的格子（游戏结束时调用）\n    public showAllHiddenGrids() {\n        if (!this.currentBoardNode) {\n            console.warn(\"⚠️ 棋盘节点不存在，无法显示隐藏格子\");\n            return;\n        }\n\n        let totalGrids = 0;\n        let restoredGrids = 0;\n        let alreadyVisibleGrids = 0;\n\n        // 遍历棋盘的所有子节点\n        const children = this.currentBoardNode.children;\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n\n            // 如果是六边形小格子节点（sixblock_q_r 格式）\n            if (child.name.startsWith(\"sixblock_\") || child.name === \"hexblock\") {\n                totalGrids++;\n\n                // 记录恢复前的状态\n                const wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;\n\n                if (wasHidden) {\n                    restoredGrids++;\n                } else {\n                    alreadyVisibleGrids++;\n                }\n\n                // 停止所有可能正在进行的动画\n                child.stopAllActions();\n\n                // 恢复显示状态\n                child.active = true;\n                child.opacity = 255;\n                child.scaleX = 1;\n                child.scaleY = 1;\n                child.angle = 0; // 重置旋转角度\n\n                // 恢复原始位置（如果有保存的话）\n                if (child['originalPosition']) {\n                    child.setPosition(child['originalPosition']);\n                }\n\n                // 确保格子可以交互\n                const button = child.getComponent(cc.Button);\n                if (button) {\n                    button.enabled = true;\n                }\n            }\n        }\n\n        if (totalGrids === 0) {\n            console.warn(\"⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确\");\n        }\n    }\n\n    // 清除所有预制体（游戏结束时调用）\n    public clearAllPrefabs() {\n        if (!this.currentBoardNode) {\n            console.warn(\"⚠️ 棋盘节点不存在，无法清除预制体\");\n            return;\n        }\n\n        let clearedCount = 0;\n        const children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组\n\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            const nodeName = child.name;\n\n            // 检查是否是需要清除的游戏预制体\n            if (this.isGamePrefab(nodeName)) {\n                child.removeFromParent();\n                clearedCount++;\n            }\n        }\n\n        // 重置格子数据中的预制体状态\n        this.hexGridData.forEach((gridData) => {\n            if (gridData.hasPlayer) {\n                gridData.hasPlayer = false;\n                gridData.playerNode = null;\n            }\n        });\n\n        // 重置炸弹爆炸标记\n        this.hasBombExploded = false;\n    }\n\n    // 判断是否为游戏预制体（需要清除的预制体）\n    private isGamePrefab(nodeName: string): boolean {\n        // 跳过六边形格子节点\n        if (nodeName.startsWith(\"sixblock_\") || nodeName === \"hexblock\") {\n            return false;\n        }\n\n        // 炸弹预制体\n        if (nodeName === \"HexBoom\") {\n            return true;\n        }\n\n        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）\n        if (nodeName.match(/^HexBoom\\d+$/)) {\n            return true;\n        }\n\n        // 标记预制体\n        if (nodeName === \"HexBiaoji\") {\n            return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * 处理点击响应，根据服务器返回的结果更新棋盘\n     * @param q 格子q坐标\n     * @param r 格子r坐标\n     * @param result 点击结果 (\"boom\" | \"safe\" | number)\n     */\n    public handleClickResponse(q: number, r: number, result: any) {\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`处理点击响应失败：坐标(${q}, ${r})无效`);\n            return;\n        }\n\n        // 如果格子上有biaoji预制体，先移除它\n        if (this.hasBiaojiAt(q, r)) {\n            // 直接移除，不播放动画\n            const gridData = this.hexGridData.get(this.getHexKey(q, r));\n            if (gridData && gridData.playerNode) {\n                gridData.playerNode.removeFromParent();\n                gridData.playerNode = null;\n            }\n        }\n\n        // 标记格子已被处理，防止重复点击\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        if (gridData) {\n            gridData.hasPlayer = true;\n        }\n\n        // 使用连锁动画的方式处理单个格子，保持一致性\n        this.playGridDisappearAnimation(q, r, result);\n    }\n\n    /**\n     * 批量处理连锁反应的格子\n     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式\n     */\n    public handleChainReaction(revealedGrids: Array<any>) {\n        if (!revealedGrids || revealedGrids.length === 0) {\n            return;\n        }\n\n       \n\n        // 同时播放所有连锁格子的消失动画，不使用延迟\n        revealedGrids.forEach((block, index) => {\n          \n\n            // 处理坐标映射：服务器可能返回 x,y 格式\n            let coordQ: number, coordR: number, neighborMines: any;\n\n            if (block.q !== undefined && block.r !== undefined) {\n                // 标准六边形坐标格式\n                coordQ = block.q;\n                coordR = block.r;\n                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;\n               \n            } else if (block.x !== undefined && block.y !== undefined) {\n                // 服务器返回x,y格式，映射为六边形坐标\n                coordQ = block.x;  // x 就是 q\n                coordR = block.y;  // y 就是 r\n                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;\n               \n            } else {\n                console.error(`   ❌ 格子${index + 1}: 无效的坐标数据:`, block);\n                console.error(`      可用字段: ${Object.keys(block).join(', ')}`);\n                return;\n            }\n\n            // 验证坐标是否为有效数字\n            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||\n                isNaN(coordQ) || isNaN(coordR)) {\n                console.error(`   ❌ 格子${index + 1}: 坐标不是有效数字: q=${coordQ}, r=${coordR}`);\n                return;\n            }\n\n            // 立即播放动画，不延迟\n            \n            this.playGridDisappearAnimation(coordQ, coordR, neighborMines);\n        });\n    }\n\n    // 播放格子消失动画并更新显示\n    private playGridDisappearAnimation(q: number, r: number, result: any) {\n        // 先隐藏格子\n        this.hideHexGridAt(q, r, false);\n\n        // 延迟显示结果，让格子消失动画先播放\n        this.scheduleOnce(() => {\n            this.updateNeighborMinesDisplay(q, r, result);\n        }, 0.3);\n    }\n\n    /**\n     * 更新指定位置的neighborMines显示（使用boom数字预制体）\n     * @param q 格子q坐标\n     * @param r 格子r坐标\n     * @param neighborMines 周围地雷数量或结果类型\n     */\n    public updateNeighborMinesDisplay(q: number, r: number, neighborMines: any) {\n        if (neighborMines === \"boom\" || neighborMines === \"mine\") {\n            // 踩到地雷，生成boom预制体并触发震动\n            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动\n            // 设置标记，表示点到了炸弹\n            this.hasBombExploded = true;\n        } else if (typeof neighborMines === \"number\" && neighborMines > 0) {\n            // 显示数字\n            this.createNumberPrefab(q, r, neighborMines);\n        } else {\n            // 如果是0、\"safe\"或其他，则不显示任何预制体\n        }\n    }\n\n    /**\n     * 处理ExtendLevelInfo消息（游戏开始时调用）\n     */\n    public onExtendLevelInfo() {\n        this.showAllHiddenGrids();\n        this.clearAllPrefabs();\n    }\n\n    /**\n     * 处理LevelGameEnd消息（游戏结束时调用）\n     * 注意：不清理任何数据，保持玩家的游玩痕迹\n     */\n    public onLevelGameEnd() {\n        // 不显示隐藏的格子，保持当前状态\n        // 不清理预制体，不重置格子状态，保持游戏结果显示\n        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等\n    }\n\n    /**\n     * 重新初始化棋盘数据（仅在开始新游戏时调用）\n     */\n    private reinitializeBoardData() {\n        // 重置hexGridData中的预制体状态\n        this.hexGridData.forEach((gridData) => {\n            gridData.hasPlayer = false;\n            gridData.playerNode = null;\n        });\n\n        // 重置炸弹爆炸标记\n        this.hasBombExploded = false;\n    }\n\n    /**\n     * 禁用所有格子的触摸事件（游戏结束时调用）\n     */\n    public disableAllGridTouch() {\n       \n\n        this.hexGridNodes.forEach((gridNode, key) => {\n            if (gridNode && cc.isValid(gridNode)) {\n                // 移除所有触摸事件监听器\n                gridNode.off(cc.Node.EventType.TOUCH_END);\n                gridNode.off(cc.Node.EventType.TOUCH_START);\n                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);\n\n                // 禁用Button组件（如果有的话）\n                const button = gridNode.getComponent(cc.Button);\n                if (button) {\n                    button.enabled = false;\n                }\n            }\n        });\n\n       \n    }\n\n    /**\n     * 检查是否点到了炸弹\n     * @returns 是否点到了炸弹\n     */\n    public hasBombExplodedInThisGame(): boolean {\n        return this.hasBombExploded;\n    }\n\n    /**\n     * 重置棋盘状态（清理所有预制体和格子状态）\n     */\n    public resetBoard() {\n      \n\n        // 显示所有隐藏的格子\n        this.showAllHiddenGrids();\n\n        // 清除所有预制体\n        this.clearAllPrefabs();\n\n        // 重新初始化棋盘数据\n        this.reinitializeBoardData();\n\n       \n    }\n\n    /**\n     * 启用所有格子的触摸事件\n     */\n    public enableAllGridTouch() {\n       \n        this.enableTouchForExistingGrids();\n    }\n\n    /**\n     * 创建自定义预制体（用于调试等特殊用途）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param prefab 预制体\n     * @param nodeName 节点名称\n     */\n    public createCustomPrefab(q: number, r: number, prefab: cc.Prefab, nodeName: string): cc.Node | null {\n        if (!prefab) {\n            console.error(`自定义预制体未设置: ${nodeName}`);\n            return null;\n        }\n\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`无效的六边形坐标: (${q}, ${r})`);\n            return null;\n        }\n\n        // 实例化预制体\n        const customNode = cc.instantiate(prefab);\n        customNode.name = nodeName;\n\n        // 设置位置\n        const position = this.getHexWorldPosition(q, r, false);\n        customNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(customNode);\n\n       \n        return customNode;\n    }\n\n    /**\n     * 重置炸弹爆炸状态（开始新游戏时调用）\n     */\n    public resetBombExplodedStatus() {\n        this.hasBombExploded = false;\n       \n    }\n\n    /**\n     * 获取当前棋盘类型\n     */\n    public getCurrentBoardType(): string {\n        return this.currentBoardType;\n    }\n\n    /**\n     * 获取当前棋盘配置（六边形版本返回简化信息）\n     */\n    public getCurrentBoardConfig(): any {\n        return {\n            boardType: this.currentBoardType,\n            gridCount: this.getHexGridCount(),\n            hasBombExploded: this.hasBombExploded\n        };\n    }\n\n    /**\n     * 获取棋盘状态信息（调试用）\n     */\n    public getHexBoardInfo() {\n        let info = {\n            validHexCoordsCount: this.validHexCoords.length,\n            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,\n            hasPlayerGamePrefab: false, // 单机模式不使用玩家头像预制体\n            hasBoardNode: !!this.currentBoardNode,\n            currentBoardType: this.currentBoardType,\n            hexGridDataSize: this.hexGridData.size,\n            hexGridNodesSize: this.hexGridNodes.size,\n            hasBombExploded: this.hasBombExploded\n        };\n\n        return info;\n    }\n\n    /**\n     * 获取前端节点的总数量（用于计算炸弹数量）\n     */\n    public getHexGridCount(): number {\n        return this.validHexCoords.length;\n    }\n\n    /**\n     * 开始新游戏时的重置方法\n     */\n    public resetForNewGame() {\n        // 显示所有隐藏的格子\n        this.showAllHiddenGrids();\n\n        // 清除所有预制体\n        this.clearAllPrefabs();\n\n        // 重新初始化棋盘数据\n        this.reinitializeBoardData();\n    }\n\n    /**\n     * 恢复棋盘状态（断线重连时使用）\n     * @param mineMap 地图状态数据\n     */\n    public restoreBoardState(mineMap: any) {\n        console.log(\"HexSingleChessBoardController: 恢复棋盘状态\", mineMap);\n\n        // 检查数据格式\n        if (mineMap && typeof mineMap === 'object') {\n            // 恢复已挖掘的方块\n            if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {\n                console.log(\"恢复已挖掘的方块数量:\", mineMap.revealedBlocks.length);\n                mineMap.revealedBlocks.forEach((block: any) => {\n                    // 六边形地图使用q和r坐标，如果没有则使用x和y\n                    const q = block.q !== undefined ? block.q : block.x;\n                    const r = block.r !== undefined ? block.r : block.y;\n                    const neighborMines = block.neighborMines;\n\n                    if (this.isValidHexCoordinate(q, r)) {\n                        console.log(`恢复已挖掘方块: (${q}, ${r}), 周围地雷数: ${neighborMines}`);\n\n                        // 确保格子被隐藏（已经点开的格子需要隐藏）\n                        this.hideHexGridAt(q, r, true);\n\n                        // 显示挖掘结果（带预制体的格子需要隐藏原格子，只显示预制体）\n                        if (neighborMines > 0) {\n                            // 延迟创建数字预制体，确保格子先隐藏\n                            this.scheduleOnce(() => {\n                                console.log(`创建数字预制体: (${q}, ${r}), 数字: ${neighborMines}`);\n                                this.createNumberPrefab(q, r, neighborMines);\n                            }, 0.05);\n                        }\n                    } else {\n                        console.warn(`❌ 坐标(${q}, ${r})无效，无法恢复方块`);\n                        console.warn(`   有效坐标数量: ${this.validHexCoords.length}`);\n                        console.warn(`   hexGridData大小: ${this.hexGridData.size}`);\n                    }\n                });\n            }\n\n            // 恢复已标记的方块\n            if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {\n                console.log(\"恢复已标记的方块数量:\", mineMap.markedBlocks.length);\n                mineMap.markedBlocks.forEach((block: any) => {\n                    // 六边形地图使用q和r坐标，如果没有则使用x和y\n                    const q = block.q !== undefined ? block.q : block.x;\n                    const r = block.r !== undefined ? block.r : block.y;\n\n                    if (this.isValidHexCoordinate(q, r)) {\n                        console.log(`恢复已标记方块: (${q}, ${r})`);\n                        // 延迟创建标记预制体\n                        this.scheduleOnce(() => {\n                            this.createBiaojiPrefab(q, r);\n                        }, 0.1);\n                    }\n                });\n            }\n        } else {\n            console.warn(\"无效的mineMap格式:\", mineMap);\n        }\n    }\n\n    /**\n     * 处理ExtendLevelInfo断线重连（恢复游戏状态）\n     * @param levelInfo 关卡信息响应数据\n     */\n    public onExtendLevelInfoReconnect(levelInfo: any) {\n        console.log(\"HexSingleChessBoardController: 处理断线重连，恢复游戏状态\");\n\n        // 只清理预制体，不重新显示格子\n        // 因为我们要根据服务器数据来决定哪些格子应该隐藏\n        this.clearAllPrefabs();\n\n        // 确保六边形坐标数据被正确初始化\n        if (levelInfo.validHexes && Array.isArray(levelInfo.validHexes)) {\n            console.log(\"使用服务器提供的validHexes数据初始化六边形坐标\");\n            this.validHexCoords = levelInfo.validHexes;\n            this.initHexBoard();\n        } else {\n            console.log(\"使用前端节点名称生成六边形坐标\");\n            this.generateCoordsFromNodeNames();\n            this.initHexBoard();\n        }\n\n        // 如果有地图状态信息，恢复棋盘状态\n        if (levelInfo.mineMap) {\n            this.restoreBoardState(levelInfo.mineMap);\n        }\n    }\n\n    /**\n     * 应用缩放到棋盘节点\n     */\n    public applyScale(scale: number) {\n        if (this.zoomTargetNode) {\n            this.zoomTargetNode.setScale(scale);\n            this.currentScale = scale;\n        }\n    }\n\n    /**\n     * 重置缩放\n     */\n    public resetZoom() {\n        this.currentScale = 1.0;\n        if (this.zoomTargetNode) {\n            this.zoomTargetNode.setScale(1.0);\n        }\n    }\n\n    /**\n     * 获取当前缩放比例\n     */\n    public getCurrentScale(): number {\n        return this.currentScale;\n    }\n}\n"]}