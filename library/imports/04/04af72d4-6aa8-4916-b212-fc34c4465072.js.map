{"version": 3, "sources": ["assets/scripts/game/Chess/SingleChessBoardController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,+DAA8D;AAC9D,iDAAgD;AAE1C,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAqB1C,SAAS;AACT,IAAM,aAAa,GAAmC;IAClD,KAAK,EAAE;QACH,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;KACjB;IACD,KAAK,EAAE;QACH,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;KACjB;IACD,KAAK,EAAE;QACH,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;KACjB;IACD,MAAM,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,EAAE;QACR,IAAI,EAAE,CAAC;QACP,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;KACjB;IACD,OAAO,EAAE;QACL,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,IAAI,EAAE,EAAE;QACR,IAAI,EAAE,EAAE;QACR,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,EAAE;KACjB;CACJ,CAAC;AAGF;IAAwD,8CAAY;IAApE;QAAA,qEAs+CC;QAn+CG,gBAAU,GAAc,IAAI,CAAC,CAAE,UAAU;QAGzC,kBAAY,GAAc,IAAI,CAAC,CAAE,YAAY;QAG7C,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAGzC,iBAAW,GAAc,IAAI,CAAC,CAAE,SAAS;QAEzC,SAAS;QAET,kBAAY,GAAY,IAAI,CAAC,CAAE,UAAU;QAGzC,kBAAY,GAAY,IAAI,CAAC,CAAE,UAAU;QAGzC,kBAAY,GAAY,IAAI,CAAC,CAAE,UAAU;QAGzC,mBAAa,GAAY,IAAI,CAAC,CAAE,WAAW;QAG3C,oBAAc,GAAY,IAAI,CAAC,CAAE,YAAY;QAE7C,YAAY;QACJ,sBAAgB,GAAY,IAAI,CAAC;QAEzC,SAAS;QACD,wBAAkB,GAAgB,IAAI,CAAC;QACvC,sBAAgB,GAAW,KAAK,CAAC,CAAE,UAAU;QAErD,SAAS;QACD,qBAAe,GAAY,KAAK,CAAC;QAEzC,SAAS;QACD,cAAQ,GAAuB,EAAE,CAAC,CAAE,aAAa;QACjD,eAAS,GAAgB,EAAE,CAAC,CAAE,aAAa;QAEnD,UAAU;QACF,mBAAa,GAAW,CAAC,CAAC;QAC1B,uBAAiB,GAAW,EAAE,CAAC;QACtB,oBAAc,GAAG,GAAG,CAAC,CAAC,YAAY;;IAq6CvD,CAAC;IAn6CG,2CAAM,GAAN;QACI,2BAA2B;IAC/B,CAAC;IAED,0CAAK,GAAL;QACI,mCAAmC;QACnC,wBAAwB;IAC5B,CAAC;IAED;;;OAGG;IACK,uDAAkB,GAA1B,UAA2B,SAAiB;QACxC,QAAQ,SAAS,EAAE;YACf,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,YAAY,CAAC;YAC7B,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,YAAY,CAAC;YAC7B,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,YAAY,CAAC;YAC7B,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,cAAc,CAAC;YAC/B;gBACI,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAED;;;OAGG;IACI,8CAAS,GAAhB,UAAiB,SAAiB;QAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;YAC3B,OAAO,CAAC,KAAK,CAAC,uDAAa,SAAW,CAAC,CAAC;YACxC,OAAO;SACV;QAED,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,+EAAiB,SAAW,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;QAEnD,SAAS;QACT,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,UAAU;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;oBAClB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzC,SAAS,EAAE,KAAK;iBACnB,CAAC;aACL;SACJ;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc;IACN,oDAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACN,gEAA2B,GAAnC;QACI,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,eAAe;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAExB,cAAc;YACd,IAAI,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;aAC9C;iBAAM;gBACH,oBAAoB;gBACpB,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,QAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;gBACrD,IAAI,QAAM,EAAE;oBACR,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBACrD,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC1D,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,CAAC,QAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBAC9C;aACJ;SACJ;IACL,CAAC;IAED,cAAc;IACN,gEAA2B,GAAnC,UAAoC,QAAgB;QAChD,mBAAmB;QACnB,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/C,IAAI,KAAK,EAAE;YACP,OAAO,EAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,2BAA2B;IACnB,kEAA6B,GAArC,UAAsC,GAAY;QAC9C,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,IAAI,CAAC;QAE1C,oBAAoB;QACpB,QAAQ,IAAI,CAAC,gBAAgB,EAAE;YAC3B,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,CAAC;YACzD,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,CAAC;YACzD,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,CAAC;YAC1D,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,CAAC,CAAC;YAC3D;gBACI,oBAAoB;gBACpB,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBACpG,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBAEtG,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;oBAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;iBACvB;gBACD,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAED,kCAAkC;IAC1B,wEAAmC,GAA3C,UAA4C,GAAY;QACpD,oBAAoB;QACpB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAChC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,UAAU;QAEhC,UAAU;QACV,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAI7C,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,kCAAkC;IAC1B,wEAAmC,GAA3C,UAA4C,GAAY;QACpD,oBAAoB;QACpB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAChC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,UAAU;QAEhC,UAAU;QACV,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAI7C,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,mCAAmC;IAC3B,yEAAoC,GAA5C,UAA6C,GAAY;QACrD,oBAAoB;QACpB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,UAAU;QAChC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAEhC,UAAU;QACV,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAI7C,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,oCAAoC;IAC5B,0EAAqC,GAA7C,UAA8C,GAAY;QACtD,oBAAoB;QACpB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAChC,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,UAAU;QAEhC,UAAU;QACV,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;QAI7C,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;aAAM;YAEH,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED,YAAY;IACJ,yDAAoB,GAA5B,UAA6B,QAAiB,EAAE,CAAS,EAAE,CAAS;QAApE,iBAmFC;QAlFG,oBAAoB;QACpB,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE7C,SAAS;QACT,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,iBAAiB,GAAa,IAAI,CAAC;QACvC,IAAI,qBAAqB,GAAG,KAAK,CAAC,CAAC,YAAY;QAC/C,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,SAAS;QAEtC,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAC,MAA2B;YACnE,cAAc,GAAG,IAAI,CAAC;YACtB,cAAc,GAAG,CAAC,CAAC;YACnB,qBAAqB,GAAG,KAAK,CAAC,CAAC,SAAS;YAIxC,SAAS;YACT,iBAAiB,GAAG;gBAChB,IAAI,cAAc,EAAE;oBAChB,cAAc,IAAI,GAAG,CAAC;oBACtB,IAAI,cAAc,IAAI,eAAe,IAAI,CAAC,qBAAqB,EAAE;wBAE7D,qBAAqB,GAAG,IAAI,CAAC,CAAC,UAAU;wBACxC,cAAc,GAAG,KAAK,CAAC,CAAC,uBAAuB;wBAE/C,SAAS;wBACT,KAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAE3B,SAAS;wBACT,IAAI,iBAAiB,EAAE;4BACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;4BACnC,iBAAiB,GAAG,IAAI,CAAC;yBAC5B;qBACJ;iBACJ;YACL,CAAC,CAAC;YACF,KAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAGhE,SAAS;YACT,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBACnC,iBAAiB,GAAG,IAAI,CAAC;aAC5B;YAED,6BAA6B;YAC7B,IAAM,kBAAkB,GAAG,cAAc;gBAChB,cAAc,GAAG,eAAe;gBAChC,CAAC,qBAAqB,CAAC;YAEhD,IAAI,kBAAkB,EAAE;gBAEpB,KAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACjC;iBAAM;aAEN;YAED,SAAS;YACT,cAAc,GAAG,KAAK,CAAC;YACvB,qBAAqB,GAAG,KAAK,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAC,MAA2B;YAGpE,SAAS;YACT,cAAc,GAAG,KAAK,CAAC;YACvB,qBAAqB,GAAG,KAAK,CAAC;YAC9B,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBACnC,iBAAiB,GAAG,IAAI,CAAC;aAC5B;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,yBAAyB;IACjB,yDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjD,YAAY;QACZ,0BAA0B;QAC1B,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACnI,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEtI,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,kBAAkB;IACV,gDAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,MAA4B;QAClE,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YAE/B,OAAO;SACV;QAED,sBAAsB;QAEtB,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,8BAA8B;IACtB,oDAAe,GAAvB,UAAwB,CAAS,EAAE,CAAS;QAGxC,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE/B,OAAO;SACV;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACxB,2BAA2B;YAE3B,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE1B,6BAA6B;YAE7B,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACrC;aAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YACvC,8BAA8B;YAE9B,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE9B,2BAA2B;YAE3B,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACrC;aAAM;YACH,6BAA6B;SAEhC;IACL,CAAC;IAED,sBAAsB;IACd,wDAAmB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC5D,UAAU;QACV,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,IAAM,WAAW,GAAM,CAAC,SAAI,CAAC,SAAI,MAAQ,CAAC;QAE1C,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,KAAK,WAAW,EAAE;YAElG,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAErC,IAAM,SAAS,GAAG;YACd,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,MAAM,CAAC,qBAAqB;SACvC,CAAC;QAGF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;IACxF,CAAC;IAED,WAAW;IACH,sDAAiB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAC1C,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,KAAK,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;IACpG,CAAC;IAED;;;;OAIG;IACI,uDAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAC1C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO;SACV;QAED,eAAe;QACf,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;QAE3B,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3C,wBAAwB;QACxB,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAClE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5E,KAAK,EAAE,CAAC;QAEb,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC;IAGhD,CAAC;IAED;;;;;OAKG;IACI,qDAAgB,GAAvB,UAAwB,CAAS,EAAE,CAAS,EAAE,aAA6B;QAA7B,8BAAA,EAAA,oBAA6B;QAGvE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;YACxE,OAAO;SACV;QAID,aAAa;QACb,IAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;QAEvB,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/B,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEzC,wBAAwB;QACxB,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAClE,IAAM,WAAW,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,gBAAgB;QACvD,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5E,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;aACrD,KAAK,EAAE,CAAC;QAEb,sBAAsB;QACtB,IAAI,aAAa,EAAE;YAEf,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;QAED,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACI,uDAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC1D,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;YAC1B,OAAO,CAAC,KAAK,CAAC,qCAAU,MAAQ,CAAC,CAAC;YAClC,OAAO;SACV;QAED,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,iBAAK,MAAM,yCAAQ,CAAC,CAAC;YACnC,OAAO;SACV;QAED,WAAW;QACX,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,SAAO,MAAQ,CAAC;QAElC,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3C,wBAAwB;QACxB,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAClE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5E,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACI,uDAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS,EAAE,MAAiB,EAAE,IAA6B;QAA7B,qBAAA,EAAA,qBAA6B;QAE5F,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;SACf;QAED,IAAI;YACA,SAAS;YAET,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC1C,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;YAGvB,OAAO;YAEP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEjC,QAAQ;YAER,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAG3C,wBAAwB;YACxB,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAElE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;iBACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,GAAG,GAAG,EAAE,MAAM,EAAE,WAAW,GAAG,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;iBACxF,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;iBACrD,KAAK,EAAE,CAAC;YAGb,OAAO,UAAU,CAAC;SACrB;QAAC,OAAO,KAAK,EAAE;YACZ,EAAE,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED,UAAU;IACF,oDAAe,GAAvB,UAAwB,MAAc;QAClC,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC;YAChC,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;SACxB;IACL,CAAC;IAED;;;;;OAKG;IACK,4DAAuB,GAA/B,UAAgC,CAAS,EAAE,CAAS;QAChD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACtB;QAED,oBAAoB;QACpB,QAAQ,IAAI,CAAC,gBAAgB,EAAE;YAC3B,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,8BAA8B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,+BAA+B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtD;gBACI,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,uBAAuB;IACf,kEAA6B,GAArC,UAAsC,CAAS,EAAE,CAAS;QACtD,gBAAgB;QAChB,uBAAuB;QACvB,iCAAiC;QACjC,iCAAiC;QACjC,qBAAqB;QACrB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,QAAQ;QAC9B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,QAAQ;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,QAAQ;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,QAAQ;QAE9B,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAEpC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,kCAAkC;IAC1B,kEAA6B,GAArC,UAAsC,CAAS,EAAE,CAAS;QACtD,aAAa;QACb,wBAAwB;QACxB,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QAEtB,QAAQ;QACR,6CAA6C;QAC7C,8CAA8C;QAE9C,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAChC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,UAAU;QAEhC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAGpC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,kCAAkC;IAC1B,kEAA6B,GAArC,UAAsC,CAAS,EAAE,CAAS;QACtD,aAAa;QACb,wBAAwB;QACxB,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QAEtB,QAAQ;QACR,6CAA6C;QAC7C,8CAA8C;QAE9C,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAChC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,UAAU;QAEhC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAGpC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,mCAAmC;IAC3B,mEAA8B,GAAtC,UAAuC,CAAS,EAAE,CAAS;QACvD,aAAa;QACb,wBAAwB;QACxB,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QAEtB,QAAQ;QACR,0CAA0C;QAC1C,6CAA6C;QAE7C,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,UAAU;QAChC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAEhC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAGpC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,oCAAoC;IAC5B,oEAA+B,GAAvC,UAAwC,CAAS,EAAE,CAAS;QACxD,aAAa;QACb,wBAAwB;QACxB,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QAEtB,QAAQ;QACR,6CAA6C;QAC7C,0CAA0C;QAE1C,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,SAAS;QAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAE,UAAU;QAChC,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,UAAU;QAEhC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAGpC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,4DAAuB,GAA/B;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAEnC,gBAAgB;QAChB,IAAM,cAAc,GAAG,EAAE,CAAC,CAAC,OAAO;QAClC,IAAM,aAAa,GAAG,GAAG,CAAC,CAAC,SAAS;QACpC,IAAM,cAAc,GAAG,EAAE,CAAC,CAAC,OAAO;QAElC,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAEnE,UAAU;QACV,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,GAAG,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,mDAAc,GAAtB,UAAuB,SAAiB,EAAE,QAAgB,EAAE,SAAiB;QACzE,SAAS;QACT,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEhE,gBAAgB;QAChB,IAAI,gBAAgB,GAAG,SAAS,CAAC;QACjC,IAAM,cAAc,GAAG,IAAI,CAAC,CAAC,SAAS;QAEtC,IAAM,eAAe,GAAG,UAAC,cAAsB;YAC3C,OAAO,EAAE,CAAC,KAAK,EAAE;iBACZ,EAAE,CAAC,KAAK,EAAE;gBACP,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;gBAClE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;aACrE,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,gBAAgB;QAChB,IAAI,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjD,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACjC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAChE,gBAAgB,IAAI,cAAc,CAAC,CAAC,WAAW;SAClD;QAED,WAAW;QACX,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE;YACf,CAAC,EAAE,gBAAgB,CAAC,CAAC;YACrB,CAAC,EAAE,gBAAgB,CAAC,CAAC;SACxB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aACxB,KAAK,EAAE,CAAC;IACb,CAAC;IAED;;OAEG;IACK,kDAAa,GAArB,UAAsB,SAAiB,EAAE,QAAgB,EAAE,SAAiB;QACxE,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,WAAW;QACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBAAE,SAAS;YAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;oBAAE,SAAS;gBAE5C,iBAAiB;gBACjB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;aAChE;SACJ;IACL,CAAC;IAED;;OAEG;IACK,kDAAa,GAArB,UAAsB,QAAiB,EAAE,SAAiB,EAAE,QAAgB,EAAE,SAAiB;QAC3F,SAAS;QACT,IAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEnD,qBAAqB;QACrB,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAExC,IAAI,CAAC,YAAY,CAAC;YACd,gBAAgB;YAChB,IAAI,gBAAgB,GAAG,SAAS,CAAC;YACjC,IAAM,cAAc,GAAG,IAAI,CAAC,CAAC,aAAa;YAE1C,IAAM,mBAAmB,GAAG,UAAC,cAAsB;gBAC/C,OAAO,EAAE,CAAC,KAAK,EAAE;qBACZ,EAAE,CAAC,IAAI,EAAE;oBACN,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;oBAClE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;iBACrE,CAAC,CAAC;YACX,CAAC,CAAC;YAEF,SAAS;YACT,IAAI,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,WAAW;YAEtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACjC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACpE,gBAAgB,IAAI,cAAc,CAAC;aACtC;YAED,WAAW;YACX,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE;gBAChB,CAAC,EAAE,gBAAgB,CAAC,CAAC;gBACrB,CAAC,EAAE,gBAAgB,CAAC,CAAC;aACxB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;iBACxB,KAAK,EAAE,CAAC;QACb,CAAC,EAAE,WAAW,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,uDAAkB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhD,WAAW;YACX,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC1D,gBAAgB;gBAChB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,SAAS;gBACT,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACpB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS;gBAE1B,kBAAkB;gBAClB,IAAI,KAAK,CAAC,kBAAkB,CAAC,EAAE;oBAC3B,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;iBAChD;gBAED,WAAW;gBACX,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;iBACzB;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,oDAAe,GAAtB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAClC,OAAO;SACV;QAED,IAAM,gBAAgB,GAAc,EAAE,CAAC;QAEvC,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhD,mBAAmB;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBACnF,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QAED,WAAW;QACX,gBAAgB,CAAC,OAAO,CAAC,UAAA,KAAK;YAC1B,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,0DAAqB,GAA7B;QACI,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO;QAIrC,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACzC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;oBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;iBACzC;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,sDAAiB,GAAxB;QACI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,mDAAc,GAArB;QAGI,kBAAkB;QAClB,0BAA0B;QAC1B,sCAAsC;IAC1C,CAAC;IAED;;;OAGG;IACI,8DAAyB,GAAhC;QAEI,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,4DAAuB,GAA9B;QAEI,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,+CAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,4DAAa,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YACxC,OAAO;SACV;QAED,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE;YACV,WAAW;YACX,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;iBACb,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACnE,IAAI,CAAC;gBACF,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YAC5B,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACI,wDAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,0DAAqB,GAA5B;QACI,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,wDAAmB,GAA1B,UAA2B,CAAS,EAAE,CAAS,EAAE,MAAW;QACxD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,wEAAe,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YAC1C,OAAO;SACV;QAID,uBAAuB;QACvB,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAExB,aAAa;YACb,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,UAAU,EAAE;gBACrB,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBACvC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;aAC9B;SACJ;QAED,kBAAkB;QAClB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;QAErC,wBAAwB;QACxB,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,2DAAsB,GAA7B,UAA8B,gBAAuB;QAArD,iBAeC;QAZG,sBAAsB;QACtB,gBAAgB,CAAC,OAAO,CAAC,UAAC,UAAU;YACxB,IAAA,CAAC,GAAuB,UAAU,EAAjC,EAAE,CAAC,GAAoB,UAAU,EAA9B,EAAE,aAAa,GAAK,UAAU,cAAf,CAAgB;YAE3C,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC/B,OAAO,CAAC,IAAI,CAAC,oEAAgB,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;gBACzC,OAAO;aACV;YAED,aAAa;YACb,KAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,wDAAmB,GAA1B,UAA2B,aAAmE;QAA9F,iBAaC;QAVG,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAE9C,OAAO;SACV;QAED,wBAAwB;QACxB,aAAa,CAAC,OAAO,CAAC,UAAC,KAAK;YACxB,aAAa;YACb,KAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,+DAA0B,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,aAAkB;QAA1E,iBA4BC;QAzBG,8BAA8B;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAExB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,UAAU,EAAE;gBACrB,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBACvC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;aAC9B;SACJ;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;SACxC;QAED,QAAQ;QACR,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAExB,yBAAyB;QACzB,qBAAqB;QACrB,IAAM,aAAa,GAAG;YAElB,KAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACI,iDAAY,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,SAA0B;QAA1B,0BAAA,EAAA,iBAA0B;QAChE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,4DAAa,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YACxC,OAAO;SACV;QAED,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE;YACV,IAAI,SAAS,EAAE;gBACX,aAAa;gBACb,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;aAC3B;iBAAM;gBACH,cAAc;gBACd,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACK,0DAAqB,GAA7B,UAA8B,QAAiB;QAC3C,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,0BAA0B;QAC1B,QAAQ,CAAC,cAAc,EAAE,CAAC;QAE1B,qBAAqB;QACrB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC/B,QAAQ,CAAC,kBAAkB,CAAC,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;SACzD;QAED,wBAAwB;QACxB,IAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEvC,+BAA+B;QAC/B,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;QAEvB,mCAAmC;QACnC,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,gBAAgB;QAEjC,QAAQ,cAAc,EAAE;YACpB,KAAK,CAAC,EAAE,KAAK;gBACT,KAAK,GAAG,CAAC,CAAC;gBACV,MAAM;YACV,KAAK,CAAC,EAAE,QAAQ;gBACZ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC7C,MAAM;YACV,KAAK,CAAC,EAAE,QAAQ;gBACZ,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC9C,MAAM;SACb;QAED,SAAS;QACT,IAAM,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB;QAExG,OAAO;QACP,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,SAAS;QAC9B,IAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,OAAO;QAC7B,IAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAE/C,YAAY;QACZ,IAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACnC,aAAa,CACV,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,aAAa,GAAG,GAAG,EAAE,CAAC,CACrD,CAAC;QAEN,aAAa;QACb,IAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpC,YAAY;aACX,EAAE,CAAC,MAAM,EAAE;YACR,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,KAAK;YAC5B,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,KAAK;SAC/B,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YACzB,YAAY;aACX,EAAE,CAAC,QAAQ,EAAE;YACV,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;YAC1D,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,GAAG,CAAC,aAAa;SAC3C,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;aACvB,IAAI,CAAC;YACF,YAAY;YACZ,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YACxB,SAAS;YACT,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC1B,+BAA+B;YAC/B,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC;QACrC,CAAC,CAAC,CAAC;QAEP,cAAc;QACd,aAAa,CAAC,KAAK,EAAE,CAAC;QACtB,aAAa,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,+DAA0B,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,aAAkB;QAGtE,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,MAAM,EAAE;YACtD,sBAAsB;YAGtB,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,uBAAuB;YAE1D,eAAe;YACf,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAE/B;aAAM,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,GAAG,CAAC,EAAE;YAC/D,OAAO;YAEP,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;SAChD;aAAM;YACH,2BAA2B;SAE9B;IACL,CAAC;IAED;;;;OAIG;IACI,mDAAc,GAArB,UAAsB,CAAS,EAAE,CAAS;QACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE;YACpF,SAAS;YACT,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;iBACxB,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACvD,IAAI,CAAC;gBACF,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBACvC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;YAC/B,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;;;;OAKG;IACI,gDAAW,GAAlB,UAAmB,CAAS,EAAE,CAAS;QACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,KAAK,CAAC;SAChB;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ,CAAC;IAC9F,CAAC;IAED;;;OAGG;IACI,0DAAqB,GAA5B;QACI,IAAM,SAAS,GAAkC,EAAE,CAAC;QAEpD,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO,SAAS,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;oBACxB,SAAS,CAAC,IAAI,CAAC,EAAC,CAAC,GAAA,EAAE,CAAC,GAAA,EAAC,CAAC,CAAC;iBAC1B;aACJ;SACJ;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,+CAAU,GAAjB;QAAA,iBAsBC;QAnBG,+BAA+B;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,WAAW;QACX,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,SAAS;QACT,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,WAAW;QACX,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,wDAAmB,GAA1B;QACI,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,IAAI,QAAQ,EAAE;oBACV,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBAC5C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;oBAC1C,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;iBAChD;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,uDAAkB,GAAzB;QACI,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,sDAAiB,GAAxB,UAAyB,OAAY;QAArC,iBAiDC;QAhDG,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,OAAO,CAAC,CAAC;QAE3D,SAAS;QACT,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACxC,WAAW;YACX,IAAI,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;gBACjE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC1D,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,KAAU;oBACtC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;oBAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;oBAClB,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;oBAE1C,IAAI,KAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBAC9B,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,2CAAa,aAAe,CAAC,CAAC;wBAE9D,uBAAuB;wBACvB,KAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;wBAE9B,gCAAgC;wBAChC,IAAI,aAAa,GAAG,CAAC,EAAE;4BACnB,oBAAoB;4BACpB,KAAI,CAAC,YAAY,CAAC;gCACd,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;4BACjD,CAAC,EAAE,IAAI,CAAC,CAAC;yBACZ;qBACJ;gBACL,CAAC,CAAC,CAAC;aACN;YAED,WAAW;YACX,IAAI,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC7D,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACxD,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,KAAU;oBACpC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;oBAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;oBAElB,IAAI,KAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBAC9B,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;wBACrC,YAAY;wBACZ,KAAI,CAAC,YAAY,CAAC;4BACd,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAClC,CAAC,EAAE,GAAG,CAAC,CAAC;qBACX;gBACL,CAAC,CAAC,CAAC;aACN;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACI,+DAA0B,GAAjC,UAAkC,SAAc;QAC5C,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,iBAAiB;QACjB,0BAA0B;QAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,mBAAmB;QACnB,IAAI,SAAS,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC7C;IACL,CAAC;IAl+CD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;oEACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACU;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oEACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oEACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oEACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;qEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;sEACa;IA9Cd,0BAA0B;QAD9C,OAAO;OACa,0BAA0B,CAs+C9C;IAAD,iCAAC;CAt+CD,AAs+CC,CAt+CuD,EAAE,CAAC,SAAS,GAs+CnE;kBAt+CoB,0BAA0B", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { WebSocketManager } from \"../../net/WebSocketManager\";\nimport { MessageId } from \"../../net/MessageId\";\n\nconst {ccclass, property} = cc._decorator;\n\n// 单机模式棋盘格子数据接口\nexport interface SingleGridData {\n    x: number;  // 格子的x坐标\n    y: number;  // 格子的y坐标\n    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置\n    hasPlayer: boolean;  // 是否已经放置了预制体\n    playerNode?: cc.Node;  // 放置的节点引用\n}\n\n// 棋盘配置接口\ninterface BoardConfig {\n    width: number;    // 棋盘宽度\n    height: number;   // 棋盘高度\n    rows: number;     // 行数\n    cols: number;     // 列数\n    gridWidth: number;  // 格子宽度\n    gridHeight: number; // 格子高度\n}\n\n// 五种棋盘配置\nconst BOARD_CONFIGS: { [key: string]: BoardConfig } = {\n    \"8x8\": {\n        width: 752,\n        height: 752,\n        rows: 8,\n        cols: 8,\n        gridWidth: 88,\n        gridHeight: 88\n    },\n    \"8x9\": {\n        width: 752,\n        height: 845,\n        rows: 9,  // 9行\n        cols: 8,  // 8列\n        gridWidth: 88,\n        gridHeight: 88\n    },\n    \"9x9\": {\n        width: 752,\n        height: 747,\n        rows: 9,\n        cols: 9,\n        gridWidth: 76,\n        gridHeight: 76\n    },\n    \"9x10\": {\n        width: 752,\n        height: 830,\n        rows: 10,  // 10行\n        cols: 9,   // 9列\n        gridWidth: 78,\n        gridHeight: 78\n    },\n    \"10x10\": {\n        width: 752,\n        height: 745,\n        rows: 10,\n        cols: 10,\n        gridWidth: 69,\n        gridHeight: 69\n    }\n};\n\n@ccclass\nexport default class SingleChessBoardController extends cc.Component {\n\n    @property(cc.Prefab)\n    boomPrefab: cc.Prefab = null;  // boom预制体\n\n    @property(cc.Prefab)\n    biaojiPrefab: cc.Prefab = null;  // biaoji预制体\n\n    @property(cc.Prefab)\n    boom1Prefab: cc.Prefab = null;  // 数字1预制体\n\n    @property(cc.Prefab)\n    boom2Prefab: cc.Prefab = null;  // 数字2预制体\n\n    @property(cc.Prefab)\n    boom3Prefab: cc.Prefab = null;  // 数字3预制体\n\n    @property(cc.Prefab)\n    boom4Prefab: cc.Prefab = null;  // 数字4预制体\n\n    @property(cc.Prefab)\n    boom5Prefab: cc.Prefab = null;  // 数字5预制体\n\n    @property(cc.Prefab)\n    boom6Prefab: cc.Prefab = null;  // 数字6预制体\n\n    @property(cc.Prefab)\n    boom7Prefab: cc.Prefab = null;  // 数字7预制体\n\n    @property(cc.Prefab)\n    boom8Prefab: cc.Prefab = null;  // 数字8预制体\n\n    // 五个棋盘节点\n    @property(cc.Node)\n    qipan8x8Node: cc.Node = null;  // 8x8棋盘节点\n\n    @property(cc.Node)\n    qipan8x9Node: cc.Node = null;  // 8x9棋盘节点\n\n    @property(cc.Node)\n    qipan9x9Node: cc.Node = null;  // 9x9棋盘节点\n\n    @property(cc.Node)\n    qipan9x10Node: cc.Node = null;  // 9x10棋盘节点\n\n    @property(cc.Node)\n    qipan10x10Node: cc.Node = null;  // 10x10棋盘节点\n\n    // 当前使用的棋盘节点\n    private currentBoardNode: cc.Node = null;\n\n    // 当前棋盘配置\n    private currentBoardConfig: BoardConfig = null;\n    private currentBoardType: string = \"8x8\";  // 默认8x8棋盘\n\n    // 炸弹爆炸标记\n    private hasBombExploded: boolean = false;\n\n    // 格子数据存储\n    private gridData: SingleGridData[][] = [];  // 二维数组存储格子数据\n    private gridNodes: cc.Node[][] = [];  // 二维数组存储格子节点\n\n    // 防重复发送消息\n    private lastClickTime: number = 0;\n    private lastClickPosition: string = \"\";\n    private readonly CLICK_COOLDOWN = 200; // 200毫秒冷却时间\n\n    onLoad() {\n        // 不进行默认初始化，等待外部调用initBoard\n    }\n\n    start() {\n        // start方法不再自动启用触摸事件，避免与initBoard重复\n        // 触摸事件的启用由initBoard方法负责\n    }\n\n    /**\n     * 根据棋盘类型获取对应的棋盘节点\n     * @param boardType 棋盘类型\n     */\n    private getBoardNodeByType(boardType: string): cc.Node | null {\n        switch (boardType) {\n            case \"8x8\":\n                return this.qipan8x8Node;\n            case \"8x9\":\n                return this.qipan8x9Node;\n            case \"9x9\":\n                return this.qipan9x9Node;\n            case \"9x10\":\n                return this.qipan9x10Node;\n            case \"10x10\":\n                return this.qipan10x10Node;\n            default:\n                return null;\n        }\n    }\n\n    /**\n     * 初始化指定类型的棋盘\n     * @param boardType 棋盘类型 (\"8x8\", \"8x9\", \"9x9\", \"9x10\", \"10x10\")\n     */\n    public initBoard(boardType: string) {\n        if (!BOARD_CONFIGS[boardType]) {\n            console.error(`不支持的棋盘类型: ${boardType}`);\n            return;\n        }\n\n        // 根据棋盘类型获取对应的节点\n        this.currentBoardNode = this.getBoardNodeByType(boardType);\n        if (!this.currentBoardNode) {\n            console.error(`棋盘节点未设置！棋盘类型: ${boardType}`);\n            return;\n        }\n\n        this.currentBoardType = boardType;\n        this.currentBoardConfig = BOARD_CONFIGS[boardType];\n\n        // 清空现有数据\n        this.gridData = [];\n        this.gridNodes = [];\n\n        // 初始化数据数组\n        for (let x = 0; x < this.currentBoardConfig.cols; x++) {\n            this.gridData[x] = [];\n            this.gridNodes[x] = [];\n            for (let y = 0; y < this.currentBoardConfig.rows; y++) {\n                this.gridData[x][y] = {\n                    x: x,\n                    y: y,\n                    worldPos: this.getGridWorldPosition(x, y),\n                    hasPlayer: false\n                };\n            }\n        }\n\n        this.createGridNodes();\n    }\n\n    // 启用现有格子的触摸事件\n    private createGridNodes() {\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置！\");\n            return;\n        }\n\n        // 如果格子已经存在，直接启用触摸事件\n        this.enableTouchForExistingGrids();\n    }\n\n    // 为现有格子启用触摸事件\n    private enableTouchForExistingGrids() {\n        // 检查棋盘节点是否存在\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置，无法启用触摸事件！\");\n            return;\n        }\n\n        // 遍历棋盘节点的所有子节点\n        let children = this.currentBoardNode.children;\n\n        for (let i = 0; i < children.length; i++) {\n            let child = children[i];\n\n            // 尝试从节点名称解析坐标\n            let coords = this.parseGridCoordinateFromName(child.name);\n            if (coords) {\n                this.setupGridTouchEvents(child, coords.x, coords.y);\n                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                this.gridNodes[coords.x][coords.y] = child;\n            } else {\n                // 如果无法从名称解析，尝试从位置计算\n                let pos = child.getPosition();\n                let coords = this.getGridCoordinateFromPosition(pos);\n                if (coords) {\n                    this.setupGridTouchEvents(child, coords.x, coords.y);\n                    this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                    this.gridNodes[coords.x][coords.y] = child;\n                }\n            }\n        }\n    }\n\n    // 从节点名称解析格子坐标\n    private parseGridCoordinateFromName(nodeName: string): {x: number, y: number} | null {\n        // 尝试匹配 Grid_x_y 格式\n        let match = nodeName.match(/Grid_(\\d+)_(\\d+)/);\n        if (match) {\n            return {x: parseInt(match[1]), y: parseInt(match[2])};\n        }\n        return null;\n    }\n\n    // 从位置计算格子坐标（需要考虑不同棋盘类型的边距）\n    private getGridCoordinateFromPosition(pos: cc.Vec2): {x: number, y: number} | null {\n        if (!this.currentBoardConfig) return null;\n\n        // 根据不同棋盘类型使用不同的计算方式\n        switch (this.currentBoardType) {\n            case \"8x9\":\n                return this.getGridCoordinateFromPositionFor8x9(pos);\n            case \"9x9\":\n                return this.getGridCoordinateFromPositionFor9x9(pos);\n            case \"9x10\":\n                return this.getGridCoordinateFromPositionFor9x10(pos);\n            case \"10x10\":\n                return this.getGridCoordinateFromPositionFor10x10(pos);\n            default:\n                // 默认计算方式（适用于其他棋盘类型）\n                let x = Math.floor((pos.x + this.currentBoardConfig.width / 2) / this.currentBoardConfig.gridWidth);\n                let y = Math.floor((pos.y + this.currentBoardConfig.height / 2) / this.currentBoardConfig.gridHeight);\n\n                if (this.isValidCoordinate(x, y)) {\n                    return {x: x, y: y};\n                }\n                return null;\n        }\n    }\n\n    // 8x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）\n    private getGridCoordinateFromPositionFor8x9(pos: cc.Vec2): {x: number, y: number} | null {\n        // 使用与预制体位置计算相同的精确参数\n        const startX = -321;  // 左下角X坐标\n        const startY = -364;  // 左下角Y坐标\n        const stepX = 91.14;  // X方向精确步长\n        const stepY = 91.125; // Y方向精确步长\n\n        // 从位置反推坐标\n        let x = Math.round((pos.x - startX) / stepX);\n        let y = Math.round((pos.y - startY) / stepY);\n\n       \n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n        return null;\n    }\n\n    // 9x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）\n    private getGridCoordinateFromPositionFor9x9(pos: cc.Vec2): {x: number, y: number} | null {\n        // 使用与预制体位置计算相同的精确参数\n        const startX = -322;  // 左下角X坐标\n        const startY = -320;  // 左下角Y坐标\n        const stepX = 80.25;  // X方向精确步长\n        const stepY = 80.375; // Y方向精确步长\n\n        // 从位置反推坐标\n        let x = Math.round((pos.x - startX) / stepX);\n        let y = Math.round((pos.y - startY) / stepY);\n\n       \n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n        return null;\n    }\n\n    // 9x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）\n    private getGridCoordinateFromPositionFor9x10(pos: cc.Vec2): {x: number, y: number} | null {\n        // 使用与预制体位置计算相同的精确参数\n        const startX = -320;  // 左下角X坐标\n        const startY = -361;  // 左下角Y坐标\n        const stepX = 80;     // X方向精确步长\n        const stepY = 80.33;  // Y方向精确步长\n\n        // 从位置反推坐标\n        let x = Math.round((pos.x - startX) / stepX);\n        let y = Math.round((pos.y - startY) / stepY);\n\n       \n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n        return null;\n    }\n\n    // 10x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）\n    private getGridCoordinateFromPositionFor10x10(pos: cc.Vec2): {x: number, y: number} | null {\n        // 使用与预制体位置计算相同的精确参数\n        const startX = -328;  // 左下角X坐标\n        const startY = -322;  // 左下角Y坐标\n        const stepX = 72.56;  // X方向精确步长\n        const stepY = 72;     // Y方向精确步长\n\n        // 从位置反推坐标\n        let x = Math.round((pos.x - startX) / stepX);\n        let y = Math.round((pos.y - startY) / stepY);\n\n       \n\n        if (this.isValidCoordinate(x, y)) {\n           \n            return {x: x, y: y};\n        } else {\n           \n            return null;\n        }\n    }\n\n    // 为格子设置触摸事件\n    private setupGridTouchEvents(gridNode: cc.Node, x: number, y: number) {\n        // 先清除已有的触摸事件，防止重复绑定\n        gridNode.off(cc.Node.EventType.TOUCH_START);\n        gridNode.off(cc.Node.EventType.TOUCH_END);\n        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);\n\n        // 长按相关变量\n        let isLongPressing = false;\n        let longPressTimer = 0;\n        let longPressCallback: Function = null;\n        let hasTriggeredLongPress = false; // 标记是否已触发长按\n        const LONG_PRESS_TIME = 1.0; // 1秒长按时间\n\n        // 触摸开始事件\n        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {\n            isLongPressing = true;\n            longPressTimer = 0;\n            hasTriggeredLongPress = false; // 重置长按标记\n\n           \n\n            // 开始长按检测\n            longPressCallback = () => {\n                if (isLongPressing) {\n                    longPressTimer += 0.1;\n                    if (longPressTimer >= LONG_PRESS_TIME && !hasTriggeredLongPress) {\n                        \n                        hasTriggeredLongPress = true; // 标记已触发长按\n                        isLongPressing = false; // 立即停止长按状态，防止触摸结束时执行点击\n\n                        // 执行长按事件\n                        this.onGridLongPress(x, y);\n\n                        // 停止长按检测\n                        if (longPressCallback) {\n                            this.unschedule(longPressCallback);\n                            longPressCallback = null;\n                        }\n                    }\n                }\n            };\n            this.schedule(longPressCallback, 0.1);\n        }, this);\n\n        // 触摸结束事件\n        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n           \n\n            // 停止长按检测\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n                longPressCallback = null;\n            }\n\n            // 严格检查：只有在所有条件都满足的情况下才执行点击事件\n            const shouldExecuteClick = isLongPressing &&\n                                     longPressTimer < LONG_PRESS_TIME &&\n                                     !hasTriggeredLongPress;\n\n            if (shouldExecuteClick) {\n                \n                this.onGridClick(x, y, event);\n            } else {\n               \n            }\n\n            // 清理长按检测\n            isLongPressing = false;\n            hasTriggeredLongPress = false;\n        }, this);\n\n        // 触摸取消事件\n        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {\n            \n\n            // 清理长按检测\n            isLongPressing = false;\n            hasTriggeredLongPress = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n                longPressCallback = null;\n            }\n        }, this);\n    }\n\n    // 计算格子的世界坐标位置（左下角为(0,0)）\n    private getGridWorldPosition(x: number, y: number): cc.Vec2 {\n        if (!this.currentBoardConfig) return cc.v2(0, 0);\n\n        // 计算格子中心点位置\n        // 左下角为(0,0)，所以y坐标需要从下往上计算\n        let posX = (x * this.currentBoardConfig.gridWidth) + (this.currentBoardConfig.gridWidth / 2) - (this.currentBoardConfig.width / 2);\n        let posY = (y * this.currentBoardConfig.gridHeight) + (this.currentBoardConfig.gridHeight / 2) - (this.currentBoardConfig.height / 2);\n        \n        return cc.v2(posX, posY);\n    }\n\n    // 格子点击事件 - 发送挖掘操作\n    private onGridClick(x: number, y: number, _event?: cc.Event.EventTouch) {\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n            return;\n        }\n\n        // 检查该位置是否已经有任何预制体（包括biaoji）\n        if (this.gridData[x][y].hasPlayer) {\n            \n            return;\n        }\n\n        // 发送LevelClickBlock消息\n        \n        this.sendLevelClickBlock(x, y, 1);\n    }\n\n    // 格子长按事件 - 标记/取消标记操作（参考联机版逻辑）\n    private onGridLongPress(x: number, y: number) {\n        \n\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n          \n            return;\n        }\n\n        // 检查该位置是否已经有biaoji预制体\n        if (this.hasBiaojiAt(x, y)) {\n            // 如果已经有biaoji，则删除它（本地立即处理）\n           \n            this.removeBiaojiAt(x, y);\n\n            // 发送取消标记消息（但不等待响应，因为已经本地处理了）\n            \n            this.sendLevelClickBlock(x, y, 2);\n        } else if (!this.gridData[x][y].hasPlayer) {\n            // 如果没有任何预制体，则生成biaoji（本地立即处理）\n           \n            this.createBiaojiPrefab(x, y);\n\n            // 发送标记消息（但不等待响应，因为已经本地处理了）\n           \n            this.sendLevelClickBlock(x, y, 2);\n        } else {\n            // 如果有其他类型的预制体（如数字、boom），则不处理\n         \n        }\n    }\n\n    // 发送LevelClickBlock消息\n    private sendLevelClickBlock(x: number, y: number, action: number) {\n        // 防重复发送检查\n        const currentTime = Date.now();\n        const positionKey = `${x},${y},${action}`;\n\n        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN && this.lastClickPosition === positionKey) {\n           \n            return;\n        }\n\n        this.lastClickTime = currentTime;\n        this.lastClickPosition = positionKey;\n\n        const clickData = {\n            x: x,\n            y: y,\n            action: action // 1=挖掘方块，2=标记/取消标记地雷\n        };\n\n        \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLevelClickBlock, clickData);\n    }\n\n    // 检查坐标是否有效\n    private isValidCoordinate(x: number, y: number): boolean {\n        if (!this.currentBoardConfig) return false;\n        return x >= 0 && x < this.currentBoardConfig.cols && y >= 0 && y < this.currentBoardConfig.rows;\n    }\n\n    /**\n     * 在指定位置创建biaoji预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     */\n    public createBiaojiPrefab(x: number, y: number) {\n        if (!this.biaojiPrefab) {\n            console.error(\"biaojiPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化biaoji预制体\n        const biaojiNode = cc.instantiate(this.biaojiPrefab);\n        biaojiNode.name = \"Biaoji\";\n\n        // 设置位置\n        const position = this.calculatePrefabPosition(x, y);\n        biaojiNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(biaojiNode);\n\n        // 播放出现动画，10x10棋盘使用0.8缩放\n        const targetScale = this.currentBoardType === \"10x10\" ? 0.8 : 1.0;\n        biaojiNode.setScale(0);\n        cc.tween(biaojiNode)\n            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })\n            .start();\n\n        // 更新格子数据\n        this.gridData[x][y].hasPlayer = true;\n        this.gridData[x][y].playerNode = biaojiNode;\n\n       \n    }\n\n    /**\n     * 在指定位置创建boom预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）\n     */\n    public createBoomPrefab(x: number, y: number, isCurrentUser: boolean = true) {\n       \n\n        if (!this.boomPrefab) {\n            console.error(\"SingleChessBoardController: boomPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        \n\n        // 实例化boom预制体\n        const boomNode = cc.instantiate(this.boomPrefab);\n        boomNode.name = \"Boom\";\n\n        // 设置位置\n        const position = this.calculatePrefabPosition(x, y);\n        boomNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(boomNode);\n\n        // 播放出现动画，10x10棋盘使用0.8缩放\n        const targetScale = this.currentBoardType === \"10x10\" ? 0.8 : 1.0;\n        const bounceScale = targetScale * 1.2; // 弹跳效果比目标缩放大20%\n        boomNode.setScale(0);\n        cc.tween(boomNode)\n            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })\n            .to(0.1, { scaleX: targetScale, scaleY: targetScale })\n            .start();\n\n        // 只有当前用户点到雷时才播放棋盘震动效果\n        if (isCurrentUser) {\n           \n            this.playBoardShakeAnimation();\n        }\n\n        // 更新格子数据\n        this.gridData[x][y].hasPlayer = true;\n        this.gridData[x][y].playerNode = boomNode;\n    }\n\n    /**\n     * 在指定位置创建数字预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param number 数字(1-8)\n     */\n    public createNumberPrefab(x: number, y: number, number: number) {\n        if (number < 1 || number > 8) {\n            console.error(`无效的数字: ${number}`);\n            return;\n        }\n\n        // 获取对应的数字预制体\n        const prefab = this.getNumberPrefab(number);\n        if (!prefab) {\n            console.error(`数字${number}预制体未设置`);\n            return;\n        }\n\n        // 实例化数字预制体\n        const numberNode = cc.instantiate(prefab);\n        numberNode.name = `Boom${number}`;\n\n        // 设置位置\n        const position = this.calculatePrefabPosition(x, y);\n        numberNode.setPosition(position);\n\n        // 添加到棋盘\n        this.currentBoardNode.addChild(numberNode);\n\n        // 播放出现动画，10x10棋盘使用0.8缩放\n        const targetScale = this.currentBoardType === \"10x10\" ? 0.8 : 1.0;\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })\n            .start();\n    }\n\n    /**\n     * 在指定位置创建自定义预制体（用于测试等功能）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param prefab 要创建的预制体\n     * @param name 节点名称\n     */\n    public createCustomPrefab(x: number, y: number, prefab: cc.Prefab, name: string = \"CustomPrefab\") {\n       \n        if (!prefab) {\n            console.error(\"预制体未设置\");\n            return null;\n        }\n\n        if (!this.currentBoardNode) {\n            console.error(\"currentBoardNode 未设置，无法添加预制体\");\n            return null;\n        }\n\n        try {\n            // 实例化预制体\n          \n            const customNode = cc.instantiate(prefab);\n            customNode.name = name;\n           \n\n            // 设置位置\n           \n            const position = this.calculatePrefabPosition(x, y);\n           \n            customNode.setPosition(position);\n\n            // 添加到棋盘\n            \n            this.currentBoardNode.addChild(customNode);\n           \n\n            // 播放出现动画，10x10棋盘使用0.8缩放\n            const targetScale = this.currentBoardType === \"10x10\" ? 0.8 : 1.0;\n           \n            customNode.setScale(0);\n            cc.tween(customNode)\n                .to(0.3, { scaleX: targetScale * 1.2, scaleY: targetScale * 1.2 }, { easing: 'backOut' })\n                .to(0.1, { scaleX: targetScale, scaleY: targetScale })\n                .start();\n\n          \n            return customNode;\n        } catch (error) {\n            cc.error(\"❌ 创建自定义预制体时发生错误:\", error);\n            return null;\n        }\n    }\n\n    // 获取数字预制体\n    private getNumberPrefab(number: number): cc.Prefab | null {\n        switch (number) {\n            case 1: return this.boom1Prefab;\n            case 2: return this.boom2Prefab;\n            case 3: return this.boom3Prefab;\n            case 4: return this.boom4Prefab;\n            case 5: return this.boom5Prefab;\n            case 6: return this.boom6Prefab;\n            case 7: return this.boom7Prefab;\n            case 8: return this.boom8Prefab;\n            default: return null;\n        }\n    }\n\n    /**\n     * 计算预制体的精确位置（参考联机版ChessBoardController）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @returns 预制体应该放置的精确位置\n     */\n    private calculatePrefabPosition(x: number, y: number): cc.Vec2 {\n        if (!this.currentBoardConfig) {\n            return cc.v2(0, 0);\n        }\n\n        // 根据不同棋盘类型使用不同的计算方式\n        switch (this.currentBoardType) {\n            case \"8x8\":\n                return this.calculatePrefabPositionFor8x8(x, y);\n            case \"8x9\":\n                return this.calculatePrefabPositionFor8x9(x, y);\n            case \"9x9\":\n                return this.calculatePrefabPositionFor9x9(x, y);\n            case \"9x10\":\n                return this.calculatePrefabPositionFor9x10(x, y);\n            case \"10x10\":\n                return this.calculatePrefabPositionFor10x10(x, y);\n            default:\n                return this.getGridWorldPosition(x, y);\n        }\n    }\n\n    // 8x8棋盘的预制体位置计算（参考联机版）\n    private calculatePrefabPositionFor8x8(x: number, y: number): cc.Vec2 {\n        // 根据联机版的坐标规律计算：\n        // (0,0) → (-314, -310)\n        // (1,0) → (-224, -310)  // x增加90\n        // (0,1) → (-314, -222)  // y增加88\n        // (7,7) → (310, 312)\n        const startX = -314;  // 起始X坐标\n        const startY = -310;  // 起始Y坐标\n        const stepX = 90;     // X方向步长\n        const stepY = 88;     // Y方向步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    // 8x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）\n    private calculatePrefabPositionFor8x9(x: number, y: number): cc.Vec2 {\n        // 根据提供的精确坐标：\n        // 左下角(0,0)：(-321, -364)\n        // 右下角(7,0)：(317, -364)\n        // 左上角(0,8)：(-321, 365)\n        // 右上角(7,8)：(317, 365)\n\n        // 计算步长：\n        // X方向步长：(317 - (-321)) / 7 = 638 / 7 ≈ 91.14\n        // Y方向步长：(365 - (-364)) / 8 = 729 / 8 ≈ 91.125\n\n        const startX = -321;  // 左下角X坐标\n        const startY = -364;  // 左下角Y坐标\n        const stepX = 91.14;  // X方向精确步长\n        const stepY = 91.125; // Y方向精确步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n      \n        return cc.v2(finalX, finalY);\n    }\n\n    // 9x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）\n    private calculatePrefabPositionFor9x9(x: number, y: number): cc.Vec2 {\n        // 根据提供的精确坐标：\n        // 左下角(0,0)：(-322, -320)\n        // 右下角(8,0)：(320, -320)\n        // 左上角(0,8)：(-322, 365)\n        // 右上角(8,8)：(320, 323)\n\n        // 计算步长：\n        // X方向步长：(320 - (-322)) / 8 = 642 / 8 = 80.25\n        // Y方向步长：(323 - (-320)) / 8 = 643 / 8 = 80.375\n\n        const startX = -322;  // 左下角X坐标\n        const startY = -320;  // 左下角Y坐标\n        const stepX = 80.25;  // X方向精确步长\n        const stepY = 80.375; // Y方向精确步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n        \n        return cc.v2(finalX, finalY);\n    }\n\n    // 9x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）\n    private calculatePrefabPositionFor9x10(x: number, y: number): cc.Vec2 {\n        // 根据提供的精确坐标：\n        // 左下角(0,0)：(-320, -361)\n        // 右下角(8,0)：(320, -361)\n        // 左上角(0,9)：(-320, 362)\n        // 右上角(8,9)：(320, 362)\n\n        // 计算步长：\n        // X方向步长：(320 - (-320)) / 8 = 640 / 8 = 80\n        // Y方向步长：(362 - (-361)) / 9 = 723 / 9 = 80.33\n\n        const startX = -320;  // 左下角X坐标\n        const startY = -361;  // 左下角Y坐标\n        const stepX = 80;     // X方向精确步长\n        const stepY = 80.33;  // Y方向精确步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n       \n        return cc.v2(finalX, finalY);\n    }\n\n    // 10x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）\n    private calculatePrefabPositionFor10x10(x: number, y: number): cc.Vec2 {\n        // 根据提供的精确坐标：\n        // 左下角(0,0)：(-328, -322)\n        // 右下角(9,0)：(325, -322)\n        // 左上角(0,9)：(-328, 326)\n        // 右上角(9,9)：(325, 326)\n\n        // 计算步长：\n        // X方向步长：(325 - (-328)) / 9 = 653 / 9 = 72.56\n        // Y方向步长：(326 - (-322)) / 9 = 648 / 9 = 72\n\n        const startX = -328;  // 左下角X坐标\n        const startY = -322;  // 左下角Y坐标\n        const stepX = 72.56;  // X方向精确步长\n        const stepY = 72;     // Y方向精确步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n       \n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 播放棋盘震动动画（包括所有小格子）\n     */\n    private playBoardShakeAnimation() {\n        if (!this.currentBoardNode) return;\n\n        // 震动参数 - 增强震动效果\n        const shakeIntensity = 30; // 震动强度\n        const shakeDuration = 1.0; // 震动持续时间\n        const shakeFrequency = 40; // 震动频率\n\n        // 震动棋盘\n        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);\n\n        // 震动所有小格子\n        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);\n    }\n\n    /**\n     * 震动棋盘节点\n     */\n    private shakeBoardNode(intensity: number, duration: number, frequency: number) {\n        // 保存原始位置\n        const originalPosition = this.currentBoardNode.position.clone();\n\n        // 创建震动动画，使用递减强度\n        let currentIntensity = intensity;\n        const intensityDecay = 0.92; // 强度衰减系数\n\n        const createShakeStep = (shakeIntensity: number) => {\n            return cc.tween()\n                .to(0.025, {\n                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,\n                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2\n                });\n        };\n\n        // 创建震动序列，强度逐渐衰减\n        let shakeTween = cc.tween(this.currentBoardNode);\n        const totalSteps = Math.floor(duration * frequency);\n\n        for (let i = 0; i < totalSteps; i++) {\n            shakeTween = shakeTween.then(createShakeStep(currentIntensity));\n            currentIntensity *= intensityDecay; // 逐渐减弱震动强度\n        }\n\n        // 最后恢复到原位置\n        shakeTween.to(0.2, {\n            x: originalPosition.x,\n            y: originalPosition.y\n        }, { easing: 'backOut' })\n        .start();\n    }\n\n    /**\n     * 震动所有小格子\n     */\n    private shakeAllGrids(intensity: number, duration: number, frequency: number) {\n        if (!this.gridNodes) return;\n\n        // 遍历所有格子节点\n        for (let x = 0; x < this.gridNodes.length; x++) {\n            if (!this.gridNodes[x]) continue;\n\n            for (let y = 0; y < this.gridNodes[x].length; y++) {\n                const gridNode = this.gridNodes[x][y];\n                if (!gridNode || !gridNode.active) continue;\n\n                // 为每个格子创建独立的震动动画\n                this.shakeGridNode(gridNode, intensity, duration, frequency);\n            }\n        }\n    }\n\n    /**\n     * 震动单个格子节点\n     */\n    private shakeGridNode(gridNode: cc.Node, intensity: number, duration: number, frequency: number) {\n        // 保存原始位置\n        const originalPosition = gridNode.position.clone();\n\n        // 为每个格子添加随机延迟，创造波浪效果\n        const randomDelay = Math.random() * 0.1;\n\n        this.scheduleOnce(() => {\n            // 创建震动动画，使用递减强度\n            let currentIntensity = intensity;\n            const intensityDecay = 0.94; // 格子震动衰减稍慢一些\n\n            const createGridShakeStep = (shakeIntensity: number) => {\n                return cc.tween()\n                    .to(0.02, {\n                        x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,\n                        y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2\n                    });\n            };\n\n            // 创建震动序列\n            let shakeTween = cc.tween(gridNode);\n            const totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短\n\n            for (let i = 0; i < totalSteps; i++) {\n                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));\n                currentIntensity *= intensityDecay;\n            }\n\n            // 最后恢复到原位置\n            shakeTween.to(0.15, {\n                x: originalPosition.x,\n                y: originalPosition.y\n            }, { easing: 'backOut' })\n            .start();\n        }, randomDelay);\n    }\n\n    /**\n     * 显示所有隐藏的格子（游戏结束时调用）\n     */\n    public showAllHiddenGrids() {\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置，无法显示隐藏格子！\");\n            return;\n        }\n\n        // 遍历棋盘的所有子节点，找到小格子并显示\n        for (let i = 0; i < this.currentBoardNode.children.length; i++) {\n            const child = this.currentBoardNode.children[i];\n\n            // 如果是小格子节点\n            if (child.name.startsWith(\"Grid_\") || child.name === \"block\") {\n                // 停止所有可能正在进行的动画\n                child.stopAllActions();\n\n                // 恢复显示状态\n                child.active = true;\n                child.opacity = 255;\n                child.scaleX = 1;\n                child.scaleY = 1;\n                child.angle = 0; // 重置旋转角度\n\n                // 恢复原始位置（如果有保存的话）\n                if (child['originalPosition']) {\n                    child.setPosition(child['originalPosition']);\n                }\n\n                // 确保格子可以交互\n                const button = child.getComponent(cc.Button);\n                if (button) {\n                    button.enabled = true;\n                }\n            }\n        }\n    }\n\n    /**\n     * 清除所有预制体（游戏结束时调用）\n     */\n    public clearAllPrefabs() {\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置，无法清除预制体！\");\n            return;\n        }\n\n        const childrenToRemove: cc.Node[] = [];\n\n        // 遍历棋盘的所有子节点\n        for (let i = 0; i < this.currentBoardNode.children.length; i++) {\n            const child = this.currentBoardNode.children[i];\n\n            // 检查是否是预制体（通过名称判断）\n            if (child.name === \"Biaoji\" || child.name === \"Boom\" || child.name.startsWith(\"Boom\")) {\n                childrenToRemove.push(child);\n            }\n        }\n\n        // 移除找到的预制体\n        childrenToRemove.forEach(child => {\n            child.removeFromParent();\n        });\n\n        // 重置格子数据\n        this.reinitializeBoardData();\n    }\n\n    /**\n     * 重新初始化棋盘数据（仅在开始新游戏时调用）\n     */\n    private reinitializeBoardData() {\n        if (!this.currentBoardConfig) return;\n\n      \n\n        // 重置gridData中的预制体状态\n        for (let x = 0; x < this.currentBoardConfig.cols; x++) {\n            for (let y = 0; y < this.currentBoardConfig.rows; y++) {\n                if (this.gridData[x] && this.gridData[x][y]) {\n                    this.gridData[x][y].hasPlayer = false;\n                    this.gridData[x][y].playerNode = null;\n                }\n            }\n        }\n    }\n\n    /**\n     * 处理ExtendLevelInfo消息（游戏结束时调用）\n     */\n    public onExtendLevelInfo() {\n        this.showAllHiddenGrids();\n        this.clearAllPrefabs();\n    }\n\n    /**\n     * 处理LevelGameEnd消息（游戏结束时调用）\n     * 注意：不清理任何数据，保持玩家的游玩痕迹\n     */\n    public onLevelGameEnd() {\n\n\n        // 不显示隐藏的格子，保持当前状态\n        // 不清理预制体，不重置格子状态，保持游戏结果显示\n        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等\n    }\n\n    /**\n     * 检查是否点到了炸弹\n     * @returns 是否点到了炸弹\n     */\n    public hasBombExplodedInThisGame(): boolean {\n       \n        return this.hasBombExploded;\n    }\n\n    /**\n     * 重置炸弹爆炸状态（开始新游戏时调用）\n     */\n    public resetBombExplodedStatus() {\n      \n        this.hasBombExploded = false;\n    }\n\n    /**\n     * 隐藏指定位置的格子（点击时调用）\n     */\n    public hideGridAt(x: number, y: number) {\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`隐藏格子失败：坐标(${x}, ${y})无效`);\n            return;\n        }\n\n        // 获取格子节点\n        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (gridNode) {\n            // 使用动画隐藏格子\n            cc.tween(gridNode)\n                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })\n                .call(() => {\n                    gridNode.active = false;\n                })\n                .start();\n        }\n    }\n\n    /**\n     * 获取当前棋盘类型\n     */\n    public getCurrentBoardType(): string {\n        return this.currentBoardType;\n    }\n\n    /**\n     * 获取当前棋盘配置\n     */\n    public getCurrentBoardConfig(): BoardConfig {\n        return this.currentBoardConfig;\n    }\n\n    /**\n     * 处理点击响应，根据服务器返回的结果更新棋盘 - 参考联机版的地图更新逻辑\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param result 点击结果 (\"boom\" | \"safe\" | number)\n     */\n    public handleClickResponse(x: number, y: number, result: any) {\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`处理点击响应失败：坐标(${x}, ${y})无效`);\n            return;\n        }\n\n     \n\n        // 如果格子上有biaoji预制体，先移除它\n        if (this.hasBiaojiAt(x, y)) {\n            \n            // 直接移除，不播放动画\n            const gridData = this.gridData[x][y];\n            if (gridData.playerNode) {\n                gridData.playerNode.removeFromParent();\n                gridData.playerNode = null;\n            }\n        }\n\n        // 标记格子已被处理，防止重复点击\n        this.gridData[x][y].hasPlayer = true;\n\n        // 使用连锁动画的方式处理单个格子，保持一致性\n        this.playGridDisappearAnimation(x, y, result);\n    }\n\n    /**\n     * 处理连锁展开结果\n     * @param floodFillResults 连锁展开数据数组\n     */\n    public handleFloodFillResults(floodFillResults: any[]) {\n\n\n        // 同时播放所有格子的消失动画，不使用延迟\n        floodFillResults.forEach((gridResult) => {\n            const { x, y, neighborMines } = gridResult;\n\n            if (!this.isValidCoordinate(x, y)) {\n                console.warn(`连锁展开跳过无效坐标: (${x}, ${y})`);\n                return;\n            }\n\n            // 立即播放动画，不延迟\n            this.playGridDisappearAnimation(x, y, neighborMines);\n        });\n    }\n\n    /**\n     * 批量处理连锁反应的格子（参考联机版的processFloodFillResult）\n     * @param revealedGrids 被揭开的格子列表 {x: number, y: number, neighborMines: number}[]\n     */\n    public handleChainReaction(revealedGrids: Array<{x: number, y: number, neighborMines: number}>) {\n\n\n        if (!revealedGrids || revealedGrids.length === 0) {\n\n            return;\n        }\n\n        // 同时播放所有连锁格子的消失动画，不使用延迟\n        revealedGrids.forEach((block) => {\n            // 立即播放动画，不延迟\n            this.playGridDisappearAnimation(block.x, block.y, block.neighborMines);\n        });\n    }\n\n    /**\n     * 播放格子消失动画（连锁效果）- 参考联机版ChessBoardController\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param neighborMines 周围地雷数量或结果类型（可以是数字、\"mine\"、\"boom\"等）\n     */\n    public playGridDisappearAnimation(x: number, y: number, neighborMines: any) {\n       \n\n        // 如果格子上有biaoji预制体，先移除它（连锁展开时）\n        if (this.hasBiaojiAt(x, y)) {\n           \n            const gridData = this.gridData[x][y];\n            if (gridData.playerNode) {\n                gridData.playerNode.removeFromParent();\n                gridData.playerNode = null;\n            }\n        }\n\n        // 标记格子已被处理（对于连锁格子）\n        if (this.isValidCoordinate(x, y)) {\n            this.gridData[x][y].hasPlayer = true;\n        }\n\n        // 先删除格子\n        this.removeGridAt(x, y);\n\n        // 延迟0.3秒后显示数字（等格子消失动画完成）\n        // 使用带标识的延迟任务，方便重置时清理\n        const delayCallback = () => {\n          \n            this.updateNeighborMinesDisplay(x, y, neighborMines);\n        };\n        this.scheduleOnce(delayCallback, 0.3);\n    }\n\n    /**\n     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）- 参考联机版\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param immediate 是否立即隐藏（不播放动画）\n     */\n    public removeGridAt(x: number, y: number, immediate: boolean = false) {\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`隐藏格子失败：坐标(${x}, ${y})无效`);\n            return;\n        }\n\n        // 获取格子节点\n        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (gridNode) {\n            if (immediate) {\n                // 立即隐藏，不播放动画\n                gridNode.active = false;\n            } else {\n                // 播放四边形格子消失动画\n                this.playGridFallAnimation(gridNode);\n            }\n        }\n    }\n\n    /**\n     * 播放四边形格子消失动画\n     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体\n     * @param gridNode 格子节点\n     */\n    private playGridFallAnimation(gridNode: cc.Node) {\n        if (!gridNode) return;\n\n        // 停止该格子上所有正在进行的动画（包括震动动画）\n        gridNode.stopAllActions();\n\n        // 保存格子的原始位置（用于重置时恢复）\n        if (!gridNode['originalPosition']) {\n            gridNode['originalPosition'] = gridNode.getPosition();\n        }\n\n        // 保存原始的zIndex，用于动画结束后恢复\n        const originalZIndex = gridNode.zIndex;\n\n        // 设置更高的层级，确保下落的格子在数字预制体和其他元素之上\n        gridNode.zIndex = 1000;\n\n        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度\n        const forceDirection = Math.floor(Math.random() * 3);\n        let moveX = 0;\n        let moveY = 200; // 向上的基础距离（增加高度）\n\n        switch (forceDirection) {\n            case 0: // 向上\n                moveX = 0;\n                break;\n            case 1: // 右上15度\n                moveX = Math.sin(20 * Math.PI / 180) * moveY;\n                break;\n            case 2: // 左上15度\n                moveX = -Math.sin(20 * Math.PI / 180) * moveY;\n                break;\n        }\n\n        // 随机旋转速度\n        const rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 360-1080度/秒，随机方向\n\n        // 动画参数\n        const upTime = 0.15; // 向上运动时间\n        const fallTime = 0.3; // 下落时间\n        const initialPosition = gridNode.getPosition();\n\n        // 创建持续旋转的动画\n        const rotationTween = cc.tween(gridNode)\n            .repeatForever(\n                cc.tween().by(0.1, { angle: rotationSpeed * 0.1 })\n            );\n\n        // 创建分阶段的运动动画\n        const movementTween = cc.tween(gridNode)\n            // 第一阶段：向上抛出\n            .to(upTime, {\n                x: initialPosition.x + moveX,\n                y: initialPosition.y + moveY\n            }, { easing: 'quadOut' })\n            // 第二阶段：自由落体\n            .to(fallTime, {\n                x: initialPosition.x + moveX + (Math.random() - 0.5) * 100, // 添加更多随机水平偏移\n                y: initialPosition.y - 500 // 下落到屏幕下方更远处\n            }, { easing: 'quadIn' })\n            .call(() => {\n                // 动画结束后隐藏格子\n                gridNode.active = false;\n                // 停止旋转动画\n                gridNode.stopAllActions();\n                // 恢复原始的zIndex（虽然格子已经隐藏，但保持一致性）\n                gridNode.zIndex = originalZIndex;\n            });\n\n        // 同时开始旋转和移动动画\n        rotationTween.start();\n        movementTween.start();\n    }\n\n    /**\n     * 更新指定位置的neighborMines显示（使用boom数字预制体）- 参考联机版\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param neighborMines 周围地雷数量或结果类型\n     */\n    public updateNeighborMinesDisplay(x: number, y: number, neighborMines: any) {\n        \n\n        if (neighborMines === \"boom\" || neighborMines === \"mine\") {\n            // 踩到地雷，生成boom预制体并触发震动\n           \n\n            this.createBoomPrefab(x, y, true); // true表示是当前用户踩到的雷，需要震动\n\n            // 设置标记，表示点到了炸弹\n            this.hasBombExploded = true;\n            \n        } else if (typeof neighborMines === \"number\" && neighborMines > 0) {\n            // 显示数字\n           \n            this.createNumberPrefab(x, y, neighborMines);\n        } else {\n            // 如果是0、\"safe\"或其他，则不显示任何预制体\n           \n        }\n    }\n\n    /**\n     * 移除指定位置的biaoji预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     */\n    public removeBiaojiAt(x: number, y: number) {\n        if (!this.isValidCoordinate(x, y)) {\n            return;\n        }\n\n        const gridData = this.gridData[x][y];\n        if (gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === \"Biaoji\") {\n            // 播放消失动画\n            cc.tween(gridData.playerNode)\n                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })\n                .call(() => {\n                    gridData.playerNode.removeFromParent();\n                    gridData.hasPlayer = false;\n                    gridData.playerNode = null;\n                })\n                .start();\n        }\n    }\n\n    /**\n     * 检查指定位置是否有biaoji预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @returns 是否有biaoji预制体\n     */\n    public hasBiaojiAt(x: number, y: number): boolean {\n        if (!this.isValidCoordinate(x, y)) {\n            return false;\n        }\n\n        const gridData = this.gridData[x][y];\n        return gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === \"Biaoji\";\n    }\n\n    /**\n     * 获取所有biaoji的位置\n     * @returns biaoji位置数组\n     */\n    public getAllBiaojiPositions(): Array<{x: number, y: number}> {\n        const positions: Array<{x: number, y: number}> = [];\n\n        if (!this.currentBoardConfig) return positions;\n\n        for (let x = 0; x < this.currentBoardConfig.cols; x++) {\n            for (let y = 0; y < this.currentBoardConfig.rows; y++) {\n                if (this.hasBiaojiAt(x, y)) {\n                    positions.push({x, y});\n                }\n            }\n        }\n\n        return positions;\n    }\n\n    /**\n     * 重置棋盘到初始状态\n     */\n    public resetBoard() {\n       \n\n        // 清理所有延迟任务（重要：防止上一局的连锁动画影响新游戏）\n        this.unscheduleAllCallbacks();\n\n        // 重置炸弹爆炸状态\n        this.resetBombExplodedStatus();\n\n        // 清除所有预制体\n        this.clearAllPrefabs();\n\n        // 重新初始化棋盘数据\n        this.reinitializeBoardData();\n\n        // 显示所有格子\n        this.showAllHiddenGrids();\n\n        // 重新启用触摸事件\n        this.scheduleOnce(() => {\n            this.enableTouchForExistingGrids();\n        }, 0.1);\n    }\n\n    /**\n     * 禁用所有格子的触摸事件（游戏结束时调用）\n     */\n    public disableAllGridTouch() {\n        if (!this.currentBoardConfig) return;\n\n        for (let x = 0; x < this.currentBoardConfig.cols; x++) {\n            for (let y = 0; y < this.currentBoardConfig.rows; y++) {\n                const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n                if (gridNode) {\n                    gridNode.off(cc.Node.EventType.TOUCH_START);\n                    gridNode.off(cc.Node.EventType.TOUCH_END);\n                    gridNode.off(cc.Node.EventType.TOUCH_CANCEL);\n                }\n            }\n        }\n    }\n\n    /**\n     * 启用所有格子的触摸事件\n     */\n    public enableAllGridTouch() {\n        this.enableTouchForExistingGrids();\n    }\n\n    /**\n     * 恢复棋盘状态（断线重连时使用）\n     * @param mineMap 地图状态数据\n     */\n    public restoreBoardState(mineMap: any) {\n        console.log(\"SingleChessBoardController: 恢复棋盘状态\", mineMap);\n\n        // 检查数据格式\n        if (mineMap && typeof mineMap === 'object') {\n            // 恢复已挖掘的方块\n            if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {\n                console.log(\"恢复已挖掘的方块数量:\", mineMap.revealedBlocks.length);\n                mineMap.revealedBlocks.forEach((block: any) => {\n                    const x = block.x;\n                    const y = block.y;\n                    const neighborMines = block.neighborMines;\n\n                    if (this.isValidCoordinate(x, y)) {\n                        console.log(`恢复已挖掘方块: (${x}, ${y}), 周围地雷数: ${neighborMines}`);\n\n                        // 确保格子被隐藏（已经点开的格子需要隐藏）\n                        this.removeGridAt(x, y, true);\n\n                        // 显示挖掘结果（带预制体的格子需要隐藏原格子，只显示预制体）\n                        if (neighborMines > 0) {\n                            // 延迟创建数字预制体，确保格子先隐藏\n                            this.scheduleOnce(() => {\n                                this.createNumberPrefab(x, y, neighborMines);\n                            }, 0.05);\n                        }\n                    }\n                });\n            }\n\n            // 恢复已标记的方块\n            if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {\n                console.log(\"恢复已标记的方块数量:\", mineMap.markedBlocks.length);\n                mineMap.markedBlocks.forEach((block: any) => {\n                    const x = block.x;\n                    const y = block.y;\n\n                    if (this.isValidCoordinate(x, y)) {\n                        console.log(`恢复已标记方块: (${x}, ${y})`);\n                        // 延迟创建标记预制体\n                        this.scheduleOnce(() => {\n                            this.createBiaojiPrefab(x, y);\n                        }, 0.1);\n                    }\n                });\n            }\n        } else {\n            console.warn(\"无效的mineMap格式:\", mineMap);\n        }\n    }\n\n    /**\n     * 处理ExtendLevelInfo断线重连（恢复游戏状态）\n     * @param levelInfo 关卡信息响应数据\n     */\n    public onExtendLevelInfoReconnect(levelInfo: any) {\n        console.log(\"SingleChessBoardController: 处理断线重连，恢复游戏状态\");\n\n        // 只清理预制体，不重新显示格子\n        // 因为我们要根据服务器数据来决定哪些格子应该隐藏\n        this.clearAllPrefabs();\n\n        // 如果有地图状态信息，恢复棋盘状态\n        if (levelInfo.mineMap) {\n            this.restoreBoardState(levelInfo.mineMap);\n        }\n    }\n}\n"]}