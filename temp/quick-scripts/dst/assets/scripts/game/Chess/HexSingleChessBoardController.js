
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '147d16sE21EH4WnxrTp2v7g', 'HexSingleChessBoardController');
// scripts/game/Chess/HexSingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexSingleChessBoardController = /** @class */ (function (_super) {
    __extends(HexSingleChessBoardController, _super);
    function HexSingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 六个六边形棋盘节点
        _this.hexBoard1Node = null; // 六边形棋盘1节点
        _this.hexBoard2Node = null; // 六边形棋盘2节点
        _this.hexBoard3Node = null; // 六边形棋盘3节点
        _this.hexBoard4Node = null; // 六边形棋盘4节点
        _this.hexBoard5Node = null; // 六边形棋盘5节点
        _this.hexBoard6Node = null; // 六边形棋盘6节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        _this.currentBoardType = "hexBoard1"; // 默认使用第一个六边形棋盘
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    HexSingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    HexSingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    HexSingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "hexBoard1":
                return this.hexBoard1Node;
            case "hexBoard2":
                return this.hexBoard2Node;
            case "hexBoard3":
                return this.hexBoard3Node;
            case "hexBoard4":
                return this.hexBoard4Node;
            case "hexBoard5":
                return this.hexBoard5Node;
            case "hexBoard6":
                return this.hexBoard6Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的六边形棋盘
     * @param boardType 棋盘类型 ("hexBoard1", "hexBoard2", "hexBoard3", "hexBoard4", "hexBoard5", "hexBoard6")
     */
    HexSingleChessBoardController.prototype.initBoard = function (boardType) {
        var _this = this;
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u516D\u8FB9\u5F62\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        this.validHexCoords = [];
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexSingleChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexSingleChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.currentBoardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.currentBoardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexSingleChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexSingleChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexSingleChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexSingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        var validGridCount = 0;
        var skippedCount = 0;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                skippedCount++;
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
                validGridCount++;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                    validGridCount++;
                }
                else {
                }
            }
        }
        if (validGridCount === 0) {
            console.warn("\u26A0\uFE0F \u6CA1\u6709\u627E\u5230\u4EFB\u4F55\u6709\u6548\u7684\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\uFF01");
            console.warn("   \u8BF7\u68C0\u67E5\u683C\u5B50\u8282\u70B9\u547D\u540D\u662F\u5426\u4E3A sixblock_q_r \u683C\u5F0F");
            console.warn("   \u4F8B\u5982: sixblock_0_0, sixblock_1_-1, sixblock_-1_2");
        }
    };
    // 从节点名称解析六边形坐标
    HexSingleChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 判断是否为游戏元素节点（需要跳过的节点）
    HexSingleChessBoardController.prototype.isGameElement = function (child, nodeName) {
        // 跳过玩家头像预制体
        if (nodeName === "player_game_pfb" || child.name.includes("Player")) {
            return true;
        }
        // 跳过boom相关预制体
        if (nodeName === "Boom" || nodeName.includes("Boom") || nodeName.includes("boom")) {
            return true;
        }
        // 跳过biaoji相关预制体
        if (nodeName === "Biaoji" || nodeName.includes("Biaoji") || nodeName.includes("biaoji")) {
            return true;
        }
        // 跳过数字预制体
        if (nodeName.match(/^\d+$/) || nodeName.includes("Number")) {
            return true;
        }
        return false;
    };
    // 测试六边形位置计算
    HexSingleChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 您提供的实际测量数据
        var testPoints = [
            { q: 0, r: 0, expected: cc.v2(-170, -165), description: "中心位置" },
            { q: 0, r: -1, expected: cc.v2(-220, -81), description: "上方" },
            { q: 1, r: -2, expected: cc.v2(-172, 2), description: "右上" },
            { q: 2, r: -3, expected: cc.v2(-122, 85), description: "右上远" },
            { q: 4, r: -4, expected: cc.v2(23, 171), description: "右上最远" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差
            if (isCorrect) {
                correctCount++;
            }
            else {
            }
        });
        // 如果不是全部正确，显示详细的数据分析
        if (correctCount < testPoints.length) {
            // 分析相邻点的差值
            for (var i = 1; i < testPoints.length; i++) {
                var prev = testPoints[i - 1];
                var curr = testPoints[i];
                var deltaQ = curr.q - prev.q;
                var deltaR = curr.r - prev.r;
                var deltaX = curr.expected.x - prev.expected.x;
                var deltaY = curr.expected.y - prev.expected.y;
            }
        }
        // 测试一些关键的推算坐标
        var extraPoints = [
            { q: 1, r: 0, description: "右侧邻居" },
            { q: -1, r: 0, description: "左侧邻居" },
            { q: 0, r: 1, description: "下方邻居" },
            { q: 1, r: -1, description: "右上邻居" },
            { q: 2, r: -2, description: "应该在(1,-2)和(2,-3)之间" }
        ];
        extraPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 验证左右间距（q方向）
        var pos1 = this.getHexWorldPosition(0, 0);
        var pos2 = this.getHexWorldPosition(1, 0);
        var actualSpacing = pos2.x - pos1.x; // 不用绝对值，看方向
    };
    // 从位置计算六边形坐标
    HexSingleChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法
        var q = Math.round(pos.x / (this.HEX_SIZE * 1.5));
        var r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);
        return { q: q, r: r };
    };
    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 根据当前棋盘类型获取配置
        var config = this.getBoardConfig(this.currentBoardType);
        if (!config) {
            console.error("\u274C \u672A\u627E\u5230\u68CB\u76D8 " + this.currentBoardType + " \u7684\u914D\u7F6E");
            return cc.v2(0, 0);
        }
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (config.exactCoords.has(key)) {
            var pos = config.exactCoords.get(key);
            // 如果是玩家头像预制体，y轴向上偏移
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y + 20);
            }
            return pos;
        }
        // 使用联机版的逻辑：每行有基准点，使用统一步长计算
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (config.rowData.has(r)) {
            var data = config.rowData.get(r);
            x = data.baseX + (q - data.baseQ) * config.uniformStepX;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式
            console.warn("\u26A0\uFE0F \u6CA1\u6709r=" + r + "\u884C\u7684\u7CBE\u786E\u6570\u636E\uFF0C\u4F7F\u7528\u901A\u7528\u516C\u5F0F");
            var stepXR = -config.uniformStepX / 2;
            var stepYR = 74;
            x = config.baseX + q * config.uniformStepX + r * stepXR;
            y = config.baseY - r * stepYR;
        }
        // 如果是玩家头像预制体，y轴向上偏移
        if (isPlayerAvatar) {
            y += 20;
        }
        return cc.v2(x, y);
    };
    // 获取棋盘配置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getBoardConfig = function (boardType) {
        var configs = new Map();
        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据
        configs.set("hexBoard1", {
            baseX: -170, baseY: -165,
            uniformStepX: 97,
            exactCoords: new Map([
                ["0,0", cc.v2(-170, -165)],
                ["0,-1", cc.v2(-220, -81)],
                ["1,-2", cc.v2(-172, 2)],
                ["2,-3", cc.v2(-122, 85)],
                ["4,-4", cc.v2(23, 171)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -170, y: -165 }],
                [-1, { baseQ: 0, baseX: -220, y: -81 }],
                [-2, { baseQ: 1, baseX: -172, y: 2 }],
                [-3, { baseQ: 2, baseX: -122, y: 85 }],
                [-4, { baseQ: 4, baseX: 23, y: 171 }] // r=-4行：基准点(4,-4) → (23, 171)
            ])
        });
        // Level_S002 (hexBoard2) - 第10关
        configs.set("hexBoard2", {
            baseX: 0, baseY: -293,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(0, -293)],
                ["0,-1", cc.v2(-50, -209)],
                ["0,-2", cc.v2(-100, -125)],
                ["0,-3", cc.v2(-150, -42)],
                ["1,-4", cc.v2(-100, 44)],
                ["2,-5", cc.v2(-50, 127)],
                ["2,-6", cc.v2(-100, 210)],
                ["3,-7", cc.v2(-50, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: 0, y: -293 }],
                [-1, { baseQ: 0, baseX: -50, y: -209 }],
                [-2, { baseQ: 0, baseX: -100, y: -125 }],
                [-3, { baseQ: 0, baseX: -150, y: -42 }],
                [-4, { baseQ: 1, baseX: -100, y: 44 }],
                [-5, { baseQ: 2, baseX: -50, y: 127 }],
                [-6, { baseQ: 2, baseX: -100, y: 210 }],
                [-7, { baseQ: 3, baseX: -50, y: 293 }] // r=-7行：基准点(3,-7) → (-50, 293)
            ])
        });
        // Level_S003 (hexBoard3) - 第15关
        configs.set("hexBoard3", {
            baseX: -146, baseY: -250,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(-146, -250)],
                ["1,1", cc.v2(0, -336)],
                ["0,-1", cc.v2(-196, -168)],
                ["1,-2", cc.v2(-146, -85)],
                ["2,-3", cc.v2(-99, -1)],
                ["1,-4", cc.v2(-246, 84)],
                ["1,-5", cc.v2(-293, 167)],
                ["2,-6", cc.v2(-246, 251)],
                ["3,-7", cc.v2(-196, 336)]
            ]),
            rowData: new Map([
                [1, { baseQ: 1, baseX: 0, y: -336 }],
                [0, { baseQ: 0, baseX: -146, y: -250 }],
                [-1, { baseQ: 0, baseX: -196, y: -168 }],
                [-2, { baseQ: 1, baseX: -146, y: -85 }],
                [-3, { baseQ: 2, baseX: -99, y: -1 }],
                [-4, { baseQ: 1, baseX: -246, y: 84 }],
                [-5, { baseQ: 1, baseX: -293, y: 167 }],
                [-6, { baseQ: 2, baseX: -246, y: 251 }],
                [-7, { baseQ: 3, baseX: -196, y: 336 }] // r=-7行：基准点(3,-7) → (-196, 336)
            ])
        });
        // Level_S004 (hexBoard4) - 第20关，同联机版
        configs.set("hexBoard4", {
            baseX: -300, baseY: -258,
            uniformStepX: 86,
            exactCoords: new Map([
                ["0,0", cc.v2(-300, -258)],
                ["1,-1", cc.v2(-258, -184)],
                ["1,-2", cc.v2(-300, -108)],
                ["2,-3", cc.v2(-258, -36)],
                ["2,-4", cc.v2(-300, 37)],
                ["3,-5", cc.v2(-258, 110)],
                ["3,-6", cc.v2(-300, 185)],
                ["4,-7", cc.v2(-258, 260)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -300, y: -258 }],
                [-1, { baseQ: 1, baseX: -258, y: -184 }],
                [-2, { baseQ: 1, baseX: -300, y: -108 }],
                [-3, { baseQ: 2, baseX: -258, y: -36 }],
                [-4, { baseQ: 2, baseX: -300, y: 37 }],
                [-5, { baseQ: 3, baseX: -258, y: 110 }],
                [-6, { baseQ: 3, baseX: -300, y: 185 }],
                [-7, { baseQ: 4, baseX: -258, y: 260 }]
            ])
        });
        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8
        configs.set("hexBoard5", {
            baseX: -257, baseY: -293,
            uniformStepX: 85.5,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-257, -293)],
                ["0,-1", cc.v2(-300, -219)],
                ["1,-2", cc.v2(-257, -146)],
                ["1,-3", cc.v2(-300, -74)],
                ["2,-4", cc.v2(-257, 0)],
                ["2,-5", cc.v2(-300, 74)],
                ["3,-6", cc.v2(-257, 146)],
                ["3,-7", cc.v2(-300, 219)],
                ["4,-8", cc.v2(-257, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -257, y: -293 }],
                [-1, { baseQ: 0, baseX: -300, y: -219 }],
                [-2, { baseQ: 1, baseX: -257, y: -146 }],
                [-3, { baseQ: 1, baseX: -300, y: -74 }],
                [-4, { baseQ: 2, baseX: -257, y: 0 }],
                [-5, { baseQ: 2, baseX: -300, y: 74 }],
                [-6, { baseQ: 3, baseX: -257, y: 146 }],
                [-7, { baseQ: 3, baseX: -300, y: 219 }],
                [-8, { baseQ: 4, baseX: -257, y: 293 }]
            ])
        });
        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8
        configs.set("hexBoard6", {
            baseX: -313, baseY: -298,
            uniformStepX: 78,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-313, -298)],
                ["1,-1", cc.v2(-274, -233)],
                ["1,-2", cc.v2(-313, -165)],
                ["2,-3", cc.v2(-274, -99)],
                ["2,-4", cc.v2(-313, -34)],
                ["3,-5", cc.v2(-274, 34)],
                ["3,-6", cc.v2(-313, 96)],
                ["4,-7", cc.v2(-274, 165)],
                ["4,-8", cc.v2(-313, 226)],
                ["5,-9", cc.v2(-274, 300)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -313, y: -298 }],
                [-1, { baseQ: 1, baseX: -274, y: -233 }],
                [-2, { baseQ: 1, baseX: -313, y: -165 }],
                [-3, { baseQ: 2, baseX: -274, y: -99 }],
                [-4, { baseQ: 2, baseX: -313, y: -34 }],
                [-5, { baseQ: 3, baseX: -274, y: 34 }],
                [-6, { baseQ: 3, baseX: -313, y: 96 }],
                [-7, { baseQ: 4, baseX: -274, y: 165 }],
                [-8, { baseQ: 4, baseX: -313, y: 226 }],
                [-9, { baseQ: 5, baseX: -274, y: 300 }]
            ])
        });
        return configs.get(boardType);
    };
    // 为六边形格子设置触摸事件
    HexSingleChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 确保节点可以接收触摸事件
        if (!gridNode.getComponent(cc.Button)) {
            // 如果没有Button组件，添加一个
            var button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈
        }
        // 移除现有的触摸事件监听器
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加点击事件监听器
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            _this.onHexGridClick(q, r, event);
        }, this);
        // 添加长按事件监听器
        this.setupLongPressEvent(gridNode, q, r);
    };
    // 设置长按事件
    HexSingleChessBoardController.prototype.setupLongPressEvent = function (gridNode, q, r) {
        var _this = this;
        var touchStartTime = 0;
        var longPressTriggered = false;
        var LONG_PRESS_DURATION = 500; // 500毫秒长按
        gridNode.on(cc.Node.EventType.TOUCH_START, function () {
            touchStartTime = Date.now();
            longPressTriggered = false;
            // 设置长按定时器
            _this.scheduleOnce(function () {
                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {
                    longPressTriggered = true;
                    _this.onHexGridLongPress(q, r);
                }
            }, LONG_PRESS_DURATION / 1000);
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_END, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexSingleChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u9884\u5236\u4F53");
            return;
        }
        // 防重复点击检查
        var currentTime = Date.now();
        var positionKey = q + "," + r;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&
            this.lastClickPosition === positionKey) {
            console.warn("点击过于频繁，忽略本次点击");
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        // 发送LevelClickBlock消息 (action = 1 表示挖掘)
        this.sendLevelClickBlock(q, r, 1);
    };
    // 六边形格子长按事件 - 发送标记操作
    HexSingleChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(q, r)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(q, r);
            // 发送取消标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else if (!gridData || !gridData.hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(q, r);
            // 发送标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
            console.warn("\u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u5176\u4ED6\u9884\u5236\u4F53\uFF0C\u65E0\u6CD5\u6807\u8BB0");
        }
    };
    // 检查六边形坐标是否有效
    HexSingleChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 发送LevelClickBlock消息（参考四边形单机控制器）
    HexSingleChessBoardController.prototype.sendLevelClickBlock = function (q, r, action) {
        var message = {
            q: q,
            r: r,
            action: action // 1 = 挖掘, 2 = 标记/取消标记
        };
        // 发送WebSocket消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, message);
    };
    // 检查指定位置是否有biaoji预制体
    HexSingleChessBoardController.prototype.hasBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            return false;
        }
        // 检查节点名称是否为Biaoji
        return gridData.playerNode.name === "HexBiaoji";
    };
    // 移除指定位置的biaoji预制体
    HexSingleChessBoardController.prototype.removeBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode &&
            gridData.playerNode.name === "HexBiaoji") {
            // 播放消失动画
            var biaojiNode_1 = gridData.playerNode;
            cc.tween(biaojiNode_1)
                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })
                .call(function () {
                biaojiNode_1.removeFromParent();
            })
                .start();
            // 更新格子数据
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        }
    };
    // 创建biaoji预制体
    HexSingleChessBoardController.prototype.createBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = biaojiNode;
        }
    };
    // 创建boom预制体
    HexSingleChessBoardController.prototype.createBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        var bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = boomNode;
        }
        // 设置标记，表示点到了炸弹
        this.hasBombExploded = true;
    };
    // 创建数字预制体
    HexSingleChessBoardController.prototype.createNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = numberNode;
        }
    };
    // 播放棋盘震动动画
    HexSingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode) {
            return;
        }
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y })
            .start();
    };
    // 隐藏指定位置的六边形小格子（点击时调用）
    HexSingleChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            console.warn("   \u5750\u6807\u7C7B\u578B: q=" + typeof q + ", r=" + typeof r);
            console.warn("   \u6709\u6548\u5750\u6807\u5217\u8868: " + this.validHexCoords.map(function (c) { return "(" + c.q + "," + c.r + ")"; }).join(', '));
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    // 播放六边形格子掉落动画
    HexSingleChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        // 保存原始位置
        var originalPos = gridNode.getPosition();
        gridNode['originalPosition'] = originalPos;
        // 播放掉落动画
        cc.tween(gridNode)
            .parallel(cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }), cc.tween().to(0.3, { opacity: 0 }), cc.tween().to(0.5, { angle: 180 }))
            .call(function () {
            gridNode.active = false;
        })
            .start();
    };
    // 显示所有隐藏的格子（游戏结束时调用）
    HexSingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法显示隐藏格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
        }
    };
    // 清除所有预制体（游戏结束时调用）
    HexSingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法清除预制体");
            return;
        }
        var clearedCount = 0;
        var children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏预制体
            if (this.isGamePrefab(nodeName)) {
                child.removeFromParent();
                clearedCount++;
            }
        }
        // 重置格子数据中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    // 判断是否为游戏预制体（需要清除的预制体）
    HexSingleChessBoardController.prototype.isGamePrefab = function (nodeName) {
        // 跳过六边形格子节点
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 炸弹预制体
        if (nodeName === "HexBoom") {
            return true;
        }
        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）
        if (nodeName.match(/^HexBoom\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName === "HexBiaoji") {
            return true;
        }
        return false;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    HexSingleChessBoardController.prototype.handleClickResponse = function (q, r, result) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(q, r)) {
            // 直接移除，不播放动画
            var gridData_1 = this.hexGridData.get(this.getHexKey(q, r));
            if (gridData_1 && gridData_1.playerNode) {
                gridData_1.playerNode.removeFromParent();
                gridData_1.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
        }
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(q, r, result);
    };
    /**
     * 批量处理连锁反应的格子
     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式
     */
    HexSingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block, index) {
            // 处理坐标映射：服务器可能返回 x,y 格式
            var coordQ, coordR, neighborMines;
            if (block.q !== undefined && block.r !== undefined) {
                // 标准六边形坐标格式
                coordQ = block.q;
                coordR = block.r;
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
            }
            else if (block.x !== undefined && block.y !== undefined) {
                // 服务器返回x,y格式，映射为六边形坐标
                coordQ = block.x; // x 就是 q
                coordR = block.y; // y 就是 r
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
            }
            else {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u65E0\u6548\u7684\u5750\u6807\u6570\u636E:", block);
                console.error("      \u53EF\u7528\u5B57\u6BB5: " + Object.keys(block).join(', '));
                return;
            }
            // 验证坐标是否为有效数字
            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||
                isNaN(coordQ) || isNaN(coordR)) {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u5750\u6807\u4E0D\u662F\u6709\u6548\u6570\u5B57: q=" + coordQ + ", r=" + coordR);
                return;
            }
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(coordQ, coordR, neighborMines);
        });
    };
    // 播放格子消失动画并更新显示
    HexSingleChessBoardController.prototype.playGridDisappearAnimation = function (q, r, result) {
        var _this = this;
        // 先隐藏格子
        this.hideHexGridAt(q, r, false);
        // 延迟显示结果，让格子消失动画先播放
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(q, r, result);
        }, 0.3);
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    HexSingleChessBoardController.prototype.updateNeighborMinesDisplay = function (q, r, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(q, r, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏开始时调用）
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    HexSingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置hexGridData中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    HexSingleChessBoardController.prototype.disableAllGridTouch = function () {
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (gridNode && cc.isValid(gridNode)) {
                // 移除所有触摸事件监听器
                gridNode.off(cc.Node.EventType.TOUCH_END);
                gridNode.off(cc.Node.EventType.TOUCH_START);
                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                // 禁用Button组件（如果有的话）
                var button = gridNode.getComponent(cc.Button);
                if (button) {
                    button.enabled = false;
                }
            }
        });
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    HexSingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置棋盘状态（清理所有预制体和格子状态）
     */
    HexSingleChessBoardController.prototype.resetBoard = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    /**
     * 启用所有格子的触摸事件
     */
    HexSingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    /**
     * 创建自定义预制体（用于调试等特殊用途）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param prefab 预制体
     * @param nodeName 节点名称
     */
    HexSingleChessBoardController.prototype.createCustomPrefab = function (q, r, prefab, nodeName) {
        if (!prefab) {
            console.error("\u81EA\u5B9A\u4E49\u9884\u5236\u4F53\u672A\u8BBE\u7F6E: " + nodeName);
            return null;
        }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return null;
        }
        // 实例化预制体
        var customNode = cc.instantiate(prefab);
        customNode.name = nodeName;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        customNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);
        return customNode;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
    };
    /**
     * 获取当前棋盘类型
     */
    HexSingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置（六边形版本返回简化信息）
     */
    HexSingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return {
            boardType: this.currentBoardType,
            gridCount: this.getHexGridCount(),
            hasBombExploded: this.hasBombExploded
        };
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexSingleChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,
            hasPlayerGamePrefab: false,
            hasBoardNode: !!this.currentBoardNode,
            currentBoardType: this.currentBoardType,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size,
            hasBombExploded: this.hasBombExploded
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexSingleChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 开始新游戏时的重置方法
     */
    HexSingleChessBoardController.prototype.resetForNewGame = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    /**
     * 恢复棋盘状态（断线重连时使用）
     * @param mineMap 地图状态数据
     */
    HexSingleChessBoardController.prototype.restoreBoardState = function (mineMap) {
        var _this = this;
        console.log("HexSingleChessBoardController: 恢复棋盘状态", mineMap);
        // 检查数据格式
        if (mineMap && typeof mineMap === 'object') {
            // 恢复已挖掘的方块
            if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {
                console.log("恢复已挖掘的方块数量:", mineMap.revealedBlocks.length);
                mineMap.revealedBlocks.forEach(function (block) {
                    // 六边形地图使用q和r坐标，如果没有则使用x和y
                    var q = block.q !== undefined ? block.q : block.x;
                    var r = block.r !== undefined ? block.r : block.y;
                    var neighborMines = block.neighborMines;
                    if (_this.isValidHexCoordinate(q, r)) {
                        console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + q + ", " + r + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                        // 确保格子被隐藏（已经点开的格子需要隐藏）
                        _this.hideHexGridAt(q, r, true);
                        // 显示挖掘结果（带预制体的格子需要隐藏原格子，只显示预制体）
                        if (neighborMines > 0) {
                            // 延迟创建数字预制体，确保格子先隐藏
                            _this.scheduleOnce(function () {
                                console.log("\u521B\u5EFA\u6570\u5B57\u9884\u5236\u4F53: (" + q + ", " + r + "), \u6570\u5B57: " + neighborMines);
                                _this.createNumberPrefab(q, r, neighborMines);
                            }, 0.05);
                        }
                    }
                    else {
                        console.warn("\u274C \u5750\u6807(" + q + ", " + r + ")\u65E0\u6548\uFF0C\u65E0\u6CD5\u6062\u590D\u65B9\u5757");
                        console.warn("   \u6709\u6548\u5750\u6807\u6570\u91CF: " + _this.validHexCoords.length);
                        console.warn("   hexGridData\u5927\u5C0F: " + _this.hexGridData.size);
                    }
                });
            }
            // 恢复已标记的方块
            if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {
                console.log("恢复已标记的方块数量:", mineMap.markedBlocks.length);
                mineMap.markedBlocks.forEach(function (block) {
                    // 六边形地图使用q和r坐标，如果没有则使用x和y
                    var q = block.q !== undefined ? block.q : block.x;
                    var r = block.r !== undefined ? block.r : block.y;
                    if (_this.isValidHexCoordinate(q, r)) {
                        console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u65B9\u5757: (" + q + ", " + r + ")");
                        // 延迟创建标记预制体
                        _this.scheduleOnce(function () {
                            _this.createBiaojiPrefab(q, r);
                        }, 0.1);
                    }
                });
            }
        }
        else {
            console.warn("无效的mineMap格式:", mineMap);
        }
    };
    /**
     * 处理ExtendLevelInfo断线重连（恢复游戏状态）
     * @param levelInfo 关卡信息响应数据
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfoReconnect = function (levelInfo) {
        console.log("HexSingleChessBoardController: 处理断线重连，恢复游戏状态");
        // 只清理预制体，不重新显示格子
        // 因为我们要根据服务器数据来决定哪些格子应该隐藏
        this.clearAllPrefabs();
        // 确保六边形坐标数据被正确初始化
        if (levelInfo.validHexes && Array.isArray(levelInfo.validHexes)) {
            console.log("使用服务器提供的validHexes数据初始化六边形坐标");
            this.validHexCoords = levelInfo.validHexes;
            this.initHexBoard();
        }
        else {
            console.log("使用前端节点名称生成六边形坐标");
            this.generateCoordsFromNodeNames();
            this.initHexBoard();
        }
        // 如果有地图状态信息，恢复棋盘状态
        if (levelInfo.mineMap) {
            this.restoreBoardState(levelInfo.mineMap);
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard1Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard2Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard3Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard4Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard5Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard6Node", void 0);
    HexSingleChessBoardController = __decorate([
        ccclass
    ], HexSingleChessBoardController);
    return HexSingleChessBoardController;
}(cc.Component));
exports.default = HexSingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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