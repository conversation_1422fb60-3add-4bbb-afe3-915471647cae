
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/ChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b2da8U/JnpOW6usOBaTL1QA', 'ChessBoardController');
// scripts/game/Chess/ChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChessBoardController = /** @class */ (function (_super) {
    __extends(ChessBoardController, _super);
    function ChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null; // player_game_pfb 预制体
        _this.boardNode = null; // 棋盘节点
        // 棋盘配置
        _this.BOARD_SIZE = 8; // 8x8棋盘
        _this.BOARD_WIDTH = 750; // 棋盘总宽度
        _this.BOARD_HEIGHT = 750; // 棋盘总高度
        _this.GRID_SIZE = 88; // 每个格子的大小 88x88
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 添加到坐标历史记录
        _this.coordinateHistory = [];
        // 自定义偏移量（如果需要调整位置）
        _this.customOffsetX = 0;
        _this.customOffsetY = -16; // 恢复原来的值，保持点击生成位置正确
        return _this;
        // update (dt) {}
    }
    ChessBoardController.prototype.onLoad = function () {
        this.initBoard();
    };
    ChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    // 初始化棋盘
    ChessBoardController.prototype.initBoard = function () {
        // 初始化数据数组
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    ChessBoardController.prototype.createGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    ChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    ChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标
    ChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        var x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 为格子节点设置触摸事件
    ChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C setupGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + x + "," + y + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    ChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    ChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1 // 1 = 挖掘
        });
    };
    // 格子长按事件 - 发送标记操作
    ChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2 // 2 = 标记
        });
    };
    // 在格子上放置玩家预制体
    ChessBoardController.prototype.placePlayerOnGrid = function (x, y, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C placePlayerOnGrid: \u65E0\u6548\u5750\u6807(" + x + "," + y + ")");
            return;
        }
        // 双重检查：确保格子为空
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error("\u274C placePlayerOnGrid: \u683C\u5B50(" + x + "," + y + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置
        var correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, function () {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 计算正确的位置（格子中心偏移(0, -16)）
    ChessBoardController.prototype.calculateCorrectPosition = function (x, y) {
        // 使用自定义偏移量
        var offsetX = this.customOffsetX;
        var offsetY = this.customOffsetY;
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    ChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        var position = cc.v2(finalX, finalY);
        return position;
    };
    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    ChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    ChessBoardController.prototype.playAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }
        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    ChessBoardController.prototype.calculateBasePositionByPlayerCount = function (x, y, totalPlayers) {
        var offsetX = this.customOffsetX;
        var offsetY;
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
        }
        else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
        }
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    ChessBoardController.prototype.calculateMultiPlayerBasePosition = function (x, y) {
        // 多人情况使用独立的偏移逻辑
        var offsetX = this.customOffsetX;
        var offsetY = 0; // 多人时不往下偏移，逻辑分开
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移（不包含往下偏移）
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    // 安全地添加玩家节点（处理Layout限制）
    ChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 方案1: 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    };
    // 备选方案：添加到父节点（Layout外部）
    ChessBoardController.prototype.addToParentNode = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        if (this.boardNode.parent) {
            // 需要转换坐标系
            var worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            var localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);
            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);
        }
        else {
            console.error("\u274C \u68CB\u76D8\u8282\u70B9\u6CA1\u6709\u7236\u8282\u70B9");
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    ChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, x, y, withFlag, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(function () {
                    }, 1.0);
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + x + "," + y + ")");
            }
            // 创建用户数据并设置头像
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    };
    // 设置玩家头像（保留原方法用于其他地方）
    ChessBoardController.prototype.setupPlayerAvatar = function (playerNode, x, y) {
        var _this = this;
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }
            // 创建用户数据
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 安全地调用setData
            try {
                playerController.setData(userData);
                // 延迟检查头像是否加载成功
                this.scheduleOnce(function () {
                    _this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);
            }
            catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");
            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    };
    // 检查头像是否加载成功
    ChessBoardController.prototype.checkAvatarLoaded = function (avatarNode, x, y) {
        if (!avatarNode) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u4E3Anull");
            return;
        }
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u6CA1\u6709Sprite\u7EC4\u4EF6");
            return;
        }
        if (!sprite.spriteFrame) {
            console.warn("\u26A0\uFE0F \u4F4D\u7F6E(" + x + "," + y + ")\u7684\u5934\u50CF\u53EF\u80FD\u52A0\u8F7D\u5931\u8D25\uFF0CspriteFrame\u4E3Anull");
            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
        }
    };
    // 设置备用头像（纯色方块）
    ChessBoardController.prototype.setFallbackAvatar = function (avatarNode, x, y) {
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var colors = [
            [255, 107, 107, 255],
            [78, 205, 196, 255],
            [69, 183, 209, 255],
            [150, 206, 180, 255],
            [255, 234, 167, 255] // 黄色
        ];
        var colorIndex = (x + y) % colors.length;
        var color = colors[colorIndex];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;
    };
    // 尝试直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetup = function (playerNode, x, y) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
        }
    };
    // 获取默认头像URL
    ChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    // 保存格子坐标（用于后续发送给后端）
    ChessBoardController.prototype.saveGridCoordinate = function (x, y) {
        // 这里可以将坐标保存到数组或发送给后端
        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);
        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    };
    // 发送坐标到服务器
    ChessBoardController.prototype.sendCoordinateToServer = function (x, y) {
        // 构造发送数据
        var moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };
        // 暂时只是打印，避免未使用变量警告
        return moveData;
    };
    ChessBoardController.prototype.addToCoordinateHistory = function (x, y) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });
    };
    // 获取当前玩家ID（示例）
    ChessBoardController.prototype.getCurrentPlayerId = function () {
        // 这里应该从全局状态或用户数据中获取
        return "player_001"; // 示例ID
    };
    // 获取指定坐标的格子数据
    ChessBoardController.prototype.getGridData = function (x, y) {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    };
    // 清除指定格子的玩家
    ChessBoardController.prototype.clearGridPlayer = function (x, y) {
        var gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    ChessBoardController.prototype.clearAllPlayers = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
    };
    // 获取所有已放置玩家的坐标
    ChessBoardController.prototype.getAllPlayerCoordinates = function () {
        var coordinates = [];
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({ x: x, y: y });
                }
            }
        }
        return coordinates;
    };
    // 检查坐标是否有效
    ChessBoardController.prototype.isValidCoordinate = function (x, y) {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    };
    // 检查格子是否为空
    ChessBoardController.prototype.isGridEmpty = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    };
    // 获取坐标历史记录
    ChessBoardController.prototype.getCoordinateHistory = function () {
        return __spreadArrays(this.coordinateHistory); // 返回副本
    };
    // 清除坐标历史记录
    ChessBoardController.prototype.clearCoordinateHistory = function () {
        this.coordinateHistory = [];
    };
    // 根据世界坐标获取格子坐标
    ChessBoardController.prototype.getGridCoordinateFromWorldPos = function (worldPos) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }
        // 将世界坐标转换为相对于棋盘的坐标
        var localPos = this.boardNode.convertToNodeSpaceAR(worldPos);
        // 计算格子坐标
        var x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 高亮显示格子（可选功能）
    ChessBoardController.prototype.highlightGrid = function (x, y, highlight) {
        if (highlight === void 0) { highlight = true; }
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
            }
            else {
                gridNode.color = cc.Color.WHITE;
            }
        }
    };
    // 批量放置玩家（用于从服务器同步数据）
    ChessBoardController.prototype.batchPlacePlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidCoordinate(coord.x, coord.y) && _this.isGridEmpty(coord.x, coord.y)) {
                _this.placePlayerOnGrid(coord.x, coord.y);
            }
        });
    };
    // 手动启用触摸事件（调试用）
    ChessBoardController.prototype.manualEnableTouch = function () {
        this.enableTouchForExistingGrids();
    };
    // 测试点击功能（调试用）
    ChessBoardController.prototype.testClick = function (x, y) {
        this.onGridClick(x, y);
    };
    // 获取棋盘状态信息（调试用）
    ChessBoardController.prototype.getBoardInfo = function () {
        var info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };
        return info;
    };
    // 简单测试方法 - 只测试位置，不加载头像
    ChessBoardController.prototype.simpleTest = function (x, y) {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }
        // 创建一个简单的彩色方块
        var testNode = new cc.Node("Test_" + x + "_" + y);
        // 添加一个彩色方块
        var sprite = testNode.addComponent(cc.Sprite);
        var texture = new cc.Texture2D();
        var color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        testNode.setContentSize(60, 60);
        // 计算位置
        var pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);
        // 添加坐标标签
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = "(" + x + "," + y + ")";
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);
        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);
    };
    // 清除所有测试节点
    ChessBoardController.prototype.clearTestNodes = function () {
        if (this.boardNode) {
            var children = this.boardNode.children.slice();
            children.forEach(function (child) {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
    };
    // 切换到父节点添加模式（如果Layout问题仍然存在）
    ChessBoardController.prototype.useParentNodeMode = function () {
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    };
    // 重新启用Layout（如果需要）
    ChessBoardController.prototype.reEnableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;
        }
    };
    // 永久禁用Layout
    ChessBoardController.prototype.disableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;
        }
    };
    // 设置自定义偏移量
    ChessBoardController.prototype.setCustomOffset = function (offsetX, offsetY) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
    };
    // 获取当前偏移量
    ChessBoardController.prototype.getCurrentOffset = function () {
        return { x: this.customOffsetX, y: this.customOffsetY };
    };
    // 测试不同偏移量
    ChessBoardController.prototype.testWithOffset = function (x, y, offsetX, offsetY) {
        // 临时保存当前偏移
        var originalOffsetX = this.customOffsetX;
        var originalOffsetY = this.customOffsetY;
        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);
        // 执行测试
        this.simpleTest(x, y);
        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    };
    // 测试头像显示功能
    ChessBoardController.prototype.testAvatarDisplay = function (x, y) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }
        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }
        // 直接调用放置玩家方法
        this.placePlayerOnGrid(x, y);
        // 延迟检查结果
        this.scheduleOnce(function () {
            var gridData = _this.gridData[x][y];
            if (gridData.playerNode) {
                // 检查PlayerGameController
                var controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                    var sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                    }
                    else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                }
                else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            }
            else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    };
    // 调试预制体结构
    ChessBoardController.prototype.debugPrefabStructure = function () {
        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }
        // 实例化一个临时节点来检查结构
        var tempNode = cc.instantiate(this.playerGamePrefab);
        // 检查组件
        var controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
            if (controller.avatar) {
                var sprite = controller.avatar.getComponent(cc.Sprite);
            }
            else {
                console.error("❌ avatar节点不存在");
            }
        }
        else {
            console.error("❌ 找不到PlayerGameController组件");
        }
        // 列出所有子节点
        this.logNodeHierarchy(tempNode, 0);
        // 清理临时节点
        tempNode.destroy();
    };
    // 递归打印节点层级
    ChessBoardController.prototype.logNodeHierarchy = function (node, depth) {
        var indent = "  ".repeat(depth);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.logNodeHierarchy(child, depth + 1);
        }
    };
    // 异步加载头像
    ChessBoardController.prototype.loadAvatarAsync = function (avatarNode, url, onComplete) {
        var _this = this;
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }
        var avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }
        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u5934\u50CF\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 设置备用头像
                _this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }
            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;
            onComplete();
        });
    };
    // 异步直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetupAsync = function (playerNode, x, y, onComplete) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
            onComplete();
        }
    };
    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    ChessBoardController.prototype.showPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID
     */
    ChessBoardController.prototype.getCurrentUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForCurrentUser = function (score) {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    };
    /**
     * 为其他用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForOtherUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点
     */
    ChessBoardController.prototype.findPlayerNodeByRecentAction = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) { // 为前5个节点或匹配的节点输出组件列表
                var allComponents = child.getComponents(cc.Component);
            }
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果
     */
    ChessBoardController.prototype.showScoreOnPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保加分/减分节点在最高层级
     */
    ChessBoardController.prototype.ensureScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    ChessBoardController.prototype.showPlayerGameSubScore = function (userId, subScore) {
        var foundPlayer = false;
        // 遍历所有格子，查找玩家节点
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                        break;
                    }
                }
            }
            if (foundPlayer)
                break;
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u8282\u70B9\u6765\u663E\u793A\u51CF\u5206\u6548\u679C: userId=" + userId);
        }
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    ChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllGrids();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    ChessBoardController.prototype.testReset = function () {
        this.resetGameScene();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    ChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        var totalChildren = this.boardNode.children.length;
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
            else {
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child, index) {
            child.removeFromParent();
        });
        // 暂时禁用强制清理，避免误删小格子
        // this.forceCleanNonGridNodes();
    };
    /**
     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）
     */
    ChessBoardController.prototype.forceCleanNonGridNodes = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 再次遍历，强制清除所有游戏预制体
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 保留条件：
            // 1. Grid_开头的节点（小格子）
            // 2. 包含Score的节点（分数控制器）
            // 3. UI相关节点
            var shouldKeep = nodeName.startsWith("Grid_") ||
                nodeName.includes("Score") ||
                nodeName.includes("score") ||
                nodeName.includes("UI") ||
                nodeName.includes("ui") ||
                nodeName.includes("Canvas") ||
                nodeName.includes("Background");
            if (!shouldKeep) {
                childrenToRemove.push(child);
            }
            // 移除找到的节点
            childrenToRemove.forEach(function (child) {
                child.removeFromParent();
            });
        }
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除
     */
    ChessBoardController.prototype.isGameElement = function (node, nodeName) {
        //  绝对不清除的节点（小格子）
        if (nodeName.startsWith("Grid_") || nodeName === "block") {
            return false;
        }
        //  分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        //  UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 🗑️明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_x_y 格式）
        if (nodeName.match(/^Test_\d+_\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent("PlayerGameController")) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        //  默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    ChessBoardController.prototype.showAllGrids = function () {
        if (!this.boardNode) {
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的小格子（点击时调用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.hideGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 使用动画隐藏格子
                cc.tween(gridNode)
                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                    .call(function () {
                    gridNode.active = false;
                })
                    .start();
            }
        }
    };
    /**
     * 重新初始化棋盘数据
     */
    ChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置gridData中的玩家状态
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
        // 清除坐标历史记录
        this.clearCoordinateHistory();
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    ChessBoardController.prototype.clearAllPlayerNodes = function () {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }
        var totalCleared = 0;
        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                }
            }
        }
        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
            }
        }
        // 移除找到的玩家预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    ChessBoardController.prototype.displayOtherPlayersAtPosition = function (x, y, actions) {
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + x + ", " + y + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            }
            else {
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    };
    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToExistingGrid = function (x, y, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.adjustMyAvatarPosition = function (x, y, position, actions) {
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = this.gridData[x][y].playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 根据总人数计算基础位置
        var basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToEmptyGrid = function (x, y, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    ChessBoardController.prototype.createOtherPlayerAtBoardPosition = function (gridX, gridY, action, relativePosition, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 根据总人数计算基础位置（统一逻辑）
        var basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);
        // 添加相对偏移
        var finalPosition = cc.v2(basePosition.x + relativePosition.x, basePosition.y + relativePosition.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, function () {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                _this.playAvatarSpawnAnimation(playerNode);
            }
            else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
        });
    };
    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.setupOtherPlayerData = function (playerNode, action, onComplete) {
        try {
            var playerController_1 = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController_1) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData_1 = this.getRealUserData(action.userId);
            if (!realUserData_1) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(function () {
                if (typeof playerController_1.setData === 'function') {
                    playerController_1.setData(realUserData_1);
                }
                // 根据操作类型设置旗子显示
                var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController_1.flagNode) {
                    playerController_1.flagNode.active = withFlag;
                }
                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u5176\u4ED6\u73A9\u5BB6\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    ChessBoardController.prototype.getPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getPlayerPositions(4);
        }
    };
    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    ChessBoardController.prototype.getGridNode = function (x, y) {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }
        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        var index = y * this.BOARD_SIZE + x;
        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }
        return null;
    };
    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    ChessBoardController.prototype.createPlayerNodeAtPosition = function (gridNode, action, position) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 检查预制体上的组件
        var components = playerNode.getComponents(cc.Component);
        components.forEach(function (comp, index) {
        });
        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
        // 添加到格子节点
        gridNode.addChild(playerNode);
        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);
    };
    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    ChessBoardController.prototype.setupPlayerNodeData = function (playerNode, action) {
        try {
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                var allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach(function (comp, index) {
                });
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }
            // 根据操作类型设置旗子显示
            var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }
            else {
                console.warn("找不到flagNode节点");
            }
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u73A9\u5BB6\u8282\u70B9\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    ChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    ChessBoardController.prototype.showScoreOnPlayerNode = function (x, y, score, showPlusOne) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
            return;
        }
        // 查找该位置的玩家节点
        var playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    };
    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    ChessBoardController.prototype.findPlayerNodeAtPosition = function (x, y) {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 计算该位置的世界坐标
        var targetPosition = this.calculateCorrectPosition(x, y);
        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        var closestNode = null;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                var distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }
        return closestNode;
    };
    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.showScoreAnimationOnNode = function (playerController, score, onComplete) {
        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.hideAvatarsAtPosition = function (x, y, onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (var gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (var gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                }
            }
        }
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        if (avatarNodes.length === 0) {
            // 没有头像需要消失
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画
        avatarNodes.forEach(function (avatarNode, index) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                    _this.clearAllMyAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    ChessBoardController.prototype.clearAllMyAvatarReferences = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    ChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    ChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    };
    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.loadNumberPrefab = function (x, y, number) {
        var prefabName = number + "boom";
        this.createTemporaryNumberNode(x, y, number);
    };
    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createTemporaryNumberNode = function (x, y, number) {
        // 创建数字显示节点
        var numberNode = new cc.Node("NeighborMines_" + number);
        var label = numberNode.addComponent(cc.Label);
        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.setupNumberNode = function (numberNode, x, y, number) {
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    ChessBoardController.prototype.playNumberAppearAnimation = function (numberNode, number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    };
    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    ChessBoardController.prototype.getNumberColor = function (number) {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    ChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    ChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    ChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    ChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    ChessBoardController.prototype.restoreOnlineMapState = function (mapData) {
        console.log("ChessBoardController: 恢复联机模式地图状态", mapData);
        // 检查数据格式
        if (Array.isArray(mapData) && mapData.length > 0) {
            if (Array.isArray(mapData[0])) {
                // 二维数组格式：mapData[x][y] 表示每个格子的状态
                console.log("检测到二维数组格式的mapData，尺寸:", mapData.length, "x", mapData[0].length);
                this.restoreFromGridArray(mapData);
            }
            else if (mapData[0] && typeof mapData[0] === 'object' && ('x' in mapData[0] && 'y' in mapData[0])) {
                // 联机模式格式：[{x, y, isRevealed, neighborMines, ...}, ...]
                console.log("检测到联机模式格式的mapData，格子数量:", mapData.length);
                this.restoreFromOnlineArray(mapData);
            }
            else {
                console.warn("未知的数组格式:", mapData[0]);
            }
        }
        else if (mapData && typeof mapData === 'object' && ('revealedBlocks' in mapData || 'markedBlocks' in mapData)) {
            // 对象格式：{revealedBlocks: [...], markedBlocks: [...]}
            console.log("检测到对象格式的mapData");
            this.restoreFromBlockLists(mapData);
        }
        else {
            console.warn("无效的mapData格式:", mapData);
        }
    };
    /**
     * 从二维数组格式恢复地图状态
     * @param mapData 二维数组格式的地图数据
     */
    ChessBoardController.prototype.restoreFromGridArray = function (mapData) {
        console.log("从二维数组格式恢复地图状态");
        // 直接遍历二维数组，但始终使用数据中的坐标，而不是数组索引
        // 因为服务端的数组结构可能与前端的坐标系统不一致
        for (var i = 0; i < mapData.length; i++) {
            for (var j = 0; j < mapData[i].length; j++) {
                var gridInfo = mapData[i][j];
                if (gridInfo && gridInfo.x !== undefined && gridInfo.y !== undefined) {
                    // 始终使用数据中的坐标
                    var actualX = gridInfo.x;
                    var actualY = gridInfo.y;
                    if (this.isValidCoordinate(actualX, actualY)) {
                        this.processGridRestore(actualX, actualY, gridInfo);
                    }
                    else {
                        console.warn("\u65E0\u6548\u5750\u6807: (" + actualX + ", " + actualY + ")");
                    }
                }
                else if (gridInfo) {
                    console.warn("\u683C\u5B50\u6570\u636E\u7F3A\u5C11\u5750\u6807\u4FE1\u606F:", gridInfo);
                }
            }
        }
    };
    /**
     * 处理单个格子的恢复
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param gridInfo 格子信息
     */
    ChessBoardController.prototype.processGridRestore = function (x, y, gridInfo) {
        var _this = this;
        if (gridInfo.isRevealed) {
            console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u662F\u5426\u5730\u96F7: " + gridInfo.isMine + ", \u5468\u56F4\u5730\u96F7\u6570: " + (gridInfo.neighborMines || 0));
            // 立即隐藏格子（不播放动画）
            this.hideGridAt(x, y, true);
            // 显示挖掘结果
            if (gridInfo.isMine) {
                // 这是一个已挖掘的地雷，创建炸弹预制体
                this.scheduleOnce(function () {
                    console.log("\u521B\u5EFA\u70B8\u5F39\u9884\u5236\u4F53: (" + x + ", " + y + ")");
                    _this.createBoomPrefab(x, y);
                }, 0.05);
            }
            else {
                // 这是一个普通格子，显示数字
                var neighborMines_1 = gridInfo.neighborMines || 0;
                if (neighborMines_1 > 0) {
                    // 延迟创建数字预制体，确保格子先隐藏
                    this.scheduleOnce(function () {
                        console.log("\u521B\u5EFA\u6570\u5B57\u9884\u5236\u4F53: (" + x + ", " + y + "), \u6570\u5B57: " + neighborMines_1);
                        _this.createNumberPrefab(x, y, neighborMines_1);
                    }, 0.05);
                }
            }
        }
        // 联机模式不应该有标记预制体，只有单机模式才有
        if (gridInfo.isMarked) {
            console.warn("\u26A0\uFE0F \u8054\u673A\u6A21\u5F0F\u68C0\u6D4B\u5230\u6807\u8BB0\u6570\u636E: (" + x + ", " + y + ")\uFF0C\u4F46\u8054\u673A\u6A21\u5F0F\u4E0D\u652F\u6301\u6807\u8BB0\u529F\u80FD");
            // 联机模式不创建标记预制体
        }
        // 恢复玩家头像
        if (gridInfo.players && Array.isArray(gridInfo.players) && gridInfo.players.length > 0) {
            console.log("\u6062\u590D\u73A9\u5BB6\u5934\u50CF: (" + x + ", " + y + "), \u73A9\u5BB6: " + gridInfo.players.join(', '));
            // 延迟创建玩家头像，确保格子状态先恢复
            this.scheduleOnce(function () {
                // 为每个玩家创建头像（通常只有一个玩家）
                gridInfo.players.forEach(function (playerId) {
                    _this.restorePlayerAvatar(x, y, playerId);
                });
            }, 0.15);
        }
    };
    /**
     * 从联机模式数组格式恢复地图状态
     * @param mapData 联机模式数组格式的地图数据
     */
    ChessBoardController.prototype.restoreFromOnlineArray = function (mapData) {
        var _this = this;
        console.log("从联机模式数组格式恢复地图状态");
        mapData.forEach(function (gridInfo) {
            var x = gridInfo.x;
            var y = gridInfo.y;
            if (_this.isValidCoordinate(x, y)) {
                if (gridInfo.isRevealed) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u5468\u56F4\u5730\u96F7\u6570: " + (gridInfo.NeighborMines || 0));
                    // 立即隐藏格子（不播放动画）
                    _this.hideGridAt(x, y, true);
                    // 显示挖掘结果
                    var neighborMines_2 = gridInfo.NeighborMines || 0;
                    if (neighborMines_2 > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createNumberPrefab(x, y, neighborMines_2);
                        }, 0.05);
                    }
                }
                if (gridInfo.isMarked) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u65B9\u5757: (" + x + ", " + y + ")");
                    // 延迟创建标记预制体
                    _this.scheduleOnce(function () {
                        _this.createBiaojiPrefab(x, y);
                    }, 0.1);
                }
            }
        });
    };
    /**
     * 从对象格式恢复地图状态
     * @param mapData 包含revealedBlocks和markedBlocks的对象
     */
    ChessBoardController.prototype.restoreFromBlockLists = function (mapData) {
        var _this = this;
        console.log("从对象格式恢复地图状态");
        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mapData.revealedBlocks.length);
            mapData.revealedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidCoordinate(x, y)) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                    // 立即隐藏格子（不播放动画）
                    _this.hideGridAt(x, y, true);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createNumberPrefab(x, y, neighborMines);
                        }, 0.05);
                    }
                }
            });
        }
        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mapData.markedBlocks.length);
            mapData.markedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                if (_this.isValidCoordinate(x, y)) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u65B9\u5757: (" + x + ", " + y + ")");
                    // 延迟创建标记预制体
                    _this.scheduleOnce(function () {
                        _this.createBiaojiPrefab(x, y);
                    }, 0.1);
                }
            });
        }
    };
    /**
     * 恢复玩家头像（断线重连时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param playerId 玩家ID
     */
    ChessBoardController.prototype.restorePlayerAvatar = function (x, y, playerId) {
        console.log("\u6062\u590D\u73A9\u5BB6\u5934\u50CF: (" + x + ", " + y + "), \u73A9\u5BB6ID: " + playerId);
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置，无法恢复玩家头像");
            return;
        }
        // 创建玩家节点
        var playerNode = cc.instantiate(this.playerGamePrefab);
        if (!playerNode) {
            console.error("创建玩家节点失败");
            return;
        }
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        playerNode.setPosition(position);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 查找用户数据
        var userData = this.findUserDataById(playerId);
        if (userData) {
            // 设置玩家数据
            this.setupPlayerAvatarAsync(playerNode, x, y, false, function () {
                // 头像加载完成的回调
                console.log("\u73A9\u5BB6\u5934\u50CF\u6062\u590D\u5B8C\u6210: (" + x + ", " + y + "), \u73A9\u5BB6: " + userData.nickName);
            });
            // 设置用户数据到PlayerGameController
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 设置用户数据（使用PlayerGameController的setData方法）
                playerController.setData(userData);
            }
        }
        else {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u6570\u636E: " + playerId);
            // 即使没有找到用户数据，也要显示节点
            playerNode.active = true;
        }
    };
    /**
     * 根据玩家ID查找用户数据
     * @param playerId 玩家ID
     * @returns 用户数据或null
     */
    ChessBoardController.prototype.findUserDataById = function (playerId) {
        try {
            var globalBean = GlobalBean_1.GlobalBean.GetInstance();
            if (globalBean.noticeStartGame && globalBean.noticeStartGame.users) {
                return globalBean.noticeStartGame.users.find(function (user) { return user.userId === playerId; });
            }
            return null;
        }
        catch (error) {
            console.error("\u67E5\u627E\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], ChessBoardController.prototype, "boardNode", void 0);
    ChessBoardController = __decorate([
        ccclass
    ], ChessBoardController);
    return ChessBoardController;
}(cc.Component));
exports.default = ChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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