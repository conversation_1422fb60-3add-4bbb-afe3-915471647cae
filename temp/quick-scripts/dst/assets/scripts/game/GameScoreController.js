
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GameScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ef3738C6EtNP63rWdv6bSAj', 'GameScoreController');
// scripts/game/GameScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var PlayerScoreController_1 = require("../pfb/PlayerScoreController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GameScoreController = /** @class */ (function (_super) {
    __extends(GameScoreController, _super);
    function GameScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scoreLayout = null; // 分数布局容器
        _this.playerScorePfb = null; // player_score_pfb 预制体
        _this._scoreControllers = []; // 分数控制器数组
        return _this;
        /**
         * 更新player_game_pfb中的change_score显示
         * @param userId 用户ID
         * @param bonusScore 奖励分数
         */
        // update (dt) {}
    }
    // onLoad () {}
    GameScoreController.prototype.onLoad = function () {
        // 初始化时不自动创建界面，等待游戏数据
    };
    GameScoreController.prototype.start = function () {
        // 不在start中自动创建，等待外部调用
    };
    /**
     * 创建分数显示界面
     * 只使用后端传回来的真实游戏数据
     */
    GameScoreController.prototype.createScoreView = function () {
        // 检查必要的组件是否存在
        if (!this.scoreLayout) {
            console.error("scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性");
            return;
        }
        if (!this.playerScorePfb) {
            console.error("playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性");
            return;
        }
        // 只使用后端传回来的真实游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息");
            return;
        }
        // 获取后端传回来的用户数据
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        // 确保所有用户都有score字段，初始化为0
        users.forEach(function (user, index) {
            if (user.score === undefined || user.score === null) {
                user.score = 0;
            }
        });
        // 清空现有的分数显示
        this.scoreLayout.removeAllChildren();
        this._scoreControllers = [];
        // 根据后端用户数据生成分数预制体
        for (var i = 0; i < users.length; i++) {
            var item = cc.instantiate(this.playerScorePfb);
            this.scoreLayout.addChild(item);
            var scoreController = item.getComponent(PlayerScoreController_1.default);
            if (scoreController) {
                this._scoreControllers.push(scoreController);
                scoreController.setData(users[i]);
            }
            else {
                console.error("预制体上没有找到 PlayerScoreController 组件");
            }
        }
    };
    /**
     * 初始化分数界面
     * 当收到 NoticeStartGame 消息后调用此方法
     */
    GameScoreController.prototype.initializeScoreView = function () {
        this.createScoreView();
    };
    /**
     * 设置游戏数据
     * 更新所有玩家的分数显示
     */
    GameScoreController.prototype.setGameData = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法设置分数数据");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        // 更新所有玩家的分数显示
        for (var i = 0; i < users.length; i++) {
            if (i < this._scoreControllers.length) {
                this._scoreControllers[i].setData(users[i]);
            }
        }
    };
    /**
     * 更新特定玩家的分数
     * @param userId 玩家ID
     * @param score 新的分数
     */
    GameScoreController.prototype.updatePlayerScore = function (userId, score) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            this._scoreControllers[userIndex].updateScore(score);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6\u6216\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex);
        }
    };
    /**
     * 更新所有玩家分数
     */
    GameScoreController.prototype.updateAllScores = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新所有玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        for (var i = 0; i < users.length && i < this._scoreControllers.length; i++) {
            this._scoreControllers[i].updateScore(users[i].score || 0);
        }
    };
    /**
     * 获取指定索引的PlayerScoreController
     * @param userIndex 用户索引
     * @returns PlayerScoreController 或 null
     */
    GameScoreController.prototype.getPlayerScoreController = function (userIndex) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            return this._scoreControllers[userIndex];
        }
        return null;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GameScoreController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理首选玩家奖励");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === data.userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 1. 更新玩家的总分数到全局数据
            users[userIndex].score = data.totalScore;
            // 2. 更新分数显示 - 显示新的总分
            this._scoreControllers[userIndex].updateScore(data.totalScore);
            // 3. 显示加分效果动画 - 显示奖励分数
            this.showAddScoreWithAnimation(userIndex, data.bonusScore);
            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isMyself = (data.userId === currentUserId);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + data.userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
            // 打印所有用户信息用于调试
            users.forEach(function (user, index) {
            });
        }
    };
    /**
     * 显示加分效果动画
     * @param userIndex 用户索引
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.showAddScoreWithAnimation = function (userIndex, bonusScore) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            var scoreController = this._scoreControllers[userIndex];
            // 调用PlayerScoreController的showAddScore方法显示加分效果
            // 这会在player_score_pfb的addscore/change_score中显示"+1"等文本
            scoreController.showAddScore(bonusScore);
        }
        else {
            console.warn("\u65E0\u6548\u7684\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u63A7\u5236\u5668\u6570\u91CF: " + this._scoreControllers.length);
        }
    };
    /**
     * 处理AI托管状态变更
     * @param userId 用户ID
     * @param isAIManaged 是否进入AI托管状态
     */
    GameScoreController.prototype.onAIStatusChange = function (userId, isAIManaged) {
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理AI托管状态变更");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 设置对应玩家的AI托管状态显示
            this._scoreControllers[userIndex].setAIManagedStatus(isAIManaged);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
        }
    };
    __decorate([
        property(cc.Node)
    ], GameScoreController.prototype, "scoreLayout", void 0);
    __decorate([
        property(cc.Prefab)
    ], GameScoreController.prototype, "playerScorePfb", void 0);
    GameScoreController = __decorate([
        ccclass
    ], GameScoreController);
    return GameScoreController;
}(cc.Component));
exports.default = GameScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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