
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/AIManagedDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '423f58DLGJCFIMOAtc3Vt+Q', 'AIManagedDialogController');
// scripts/game/AIManagedDialogController.ts

"use strict";
// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AIManagedDialogController = /** @class */ (function (_super) {
    __extends(AIManagedDialogController, _super);
    function AIManagedDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null; // 背景板
        _this.maskNode = null; // 遮罩节点，用于接收点击事件
        _this.isShowing = false; // 是否正在显示
        return _this;
    }
    AIManagedDialogController.prototype.start = function () {
        // 初始化时隐藏
        this.node.active = false;
        // 如果 boardBg 没有设置，尝试查找子节点
        if (!this.boardBg) {
            var bgNode = this.node.getChildByName('bot_bg');
            if (bgNode) {
                this.boardBg = bgNode;
            }
            else {
                console.warn("未找到 bot_bg 子节点");
            }
        }
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            // 检查是否有 BlockInputEvents 组件，如果有则禁用它
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
            }
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        else {
            console.warn("maskNode 未设置");
        }
        // 同时为主节点添加点击事件监听作为备用
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
    };
    /**
     * 显示托管中页面
     */
    AIManagedDialogController.prototype.show = function () {
        if (this.isShowing) {
            return;
        }
        this.isShowing = true;
        this.node.active = true;
        // 禁用 BlockInputEvents 组件以允许点击事件
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
            }
        }
        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
        }
        else {
            console.warn("❌ boardBg 节点未设置");
        }
        // 执行显示动画
        this.playShowAnimation();
    };
    /**
     * 隐藏托管中页面
     */
    AIManagedDialogController.prototype.hide = function () {
        var _this = this;
        if (!this.isShowing) {
            return;
        }
        this.isShowing = false;
        // 重新启用 BlockInputEvents 组件（如果存在）
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = true;
            }
        }
        // 执行隐藏动画
        this.playHideAnimation(function () {
            _this.node.active = false;
        });
    };
    /**
     * 播放显示动画
     */
    AIManagedDialogController.prototype.playShowAnimation = function () {
        if (!this.boardBg) {
            console.warn("❌ boardBg 为空，无法播放动画");
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 1,
            opacity: 255
        }, {
            easing: 'backOut'
        })
            .call(function () {
        })
            .start();
    };
    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    AIManagedDialogController.prototype.playHideAnimation = function (callback) {
        if (!this.boardBg) {
            if (callback)
                callback();
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 0,
            opacity: 0
        }, {
            easing: 'backIn'
        })
            .call(function () {
            if (callback)
                callback();
        })
            .start();
    };
    /**
     * 遮罩点击事件处理
     */
    AIManagedDialogController.prototype.onMaskClick = function () {
        this.handleClick();
    };
    /**
     * 主节点点击事件处理
     */
    AIManagedDialogController.prototype.onNodeClick = function () {
        this.handleClick();
    };
    /**
     * 统一的点击处理逻辑
     */
    AIManagedDialogController.prototype.handleClick = function () {
        if (!this.isShowing) {
            return;
        }
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    };
    /**
     * 发送取消AI托管消息
     */
    AIManagedDialogController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
    };
    /**
     * 检查是否正在显示
     */
    AIManagedDialogController.prototype.isVisible = function () {
        return this.isShowing && this.node.active;
    };
    AIManagedDialogController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "maskNode", void 0);
    AIManagedDialogController = __decorate([
        ccclass
    ], AIManagedDialogController);
    return AIManagedDialogController;
}(cc.Component));
exports.default = AIManagedDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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