
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/MessageId.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f1db1fNQYxAjIe8lTfdT1iF', 'MessageId');
// scripts/net/MessageId.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageId = void 0;
//消息 id
var MessageId;
(function (MessageId) {
    MessageId["MsgTypeCreateWs"] = "CreateWs";
    MessageId["MsgTypeNoticeUserCoin"] = "NoticeUserCoin";
    MessageId["MsgTypeHeartbeat"] = "Heartbeat";
    MessageId["MsgTypeLogin"] = "Login";
    MessageId["MsgTypeUserInfo"] = "UserInfo";
    MessageId["MsgTypePairRequest"] = "PairRequest";
    MessageId["MsgTypeCancelPair"] = "CancelPair";
    MessageId["MsgTypePairResult"] = "PairResult";
    MessageId["MsgTypeEnterRoom"] = "EnterRoom";
    MessageId["MsgTypeSitDown"] = "SitDown";
    MessageId["MsgTypeRobotSitDown"] = "RobotSitDown";
    MessageId["MsgTypeStand"] = "Stand";
    MessageId["MsgTypeReady"] = "Ready";
    MessageId["MsgTypeLeaveRoom"] = "LeaveRoom";
    MessageId["MsgTypeUserOffline"] = "UserOffline";
    MessageId["MsgTypeKickOutUser"] = "KickOutUser";
    MessageId["MsgTypeCreateInvite"] = "CreateInvite";
    MessageId["MsgTypeAcceptInvite"] = "AcceptInvite";
    MessageId["MsgTypeInviteReady"] = "InviteReady";
    MessageId["MsgTypeChgInviteCfg"] = "ChgInviteCfg";
    MessageId["MsgTypeLeaveInvite"] = "LeaveInvite";
    MessageId["MsgTypeNoticeInviteStatus"] = "NoticeInviteStatus";
    MessageId["MsgTypeInviteKickOut"] = "InviteKickOut";
    MessageId["MsgTypeInviteStart"] = "InviteStart";
    MessageId["MsgTypeViewerList"] = "ViewerList";
    MessageId["MsgTypeGameStart"] = "GameStart";
    MessageId["MsgTypeFirstMove"] = "FirstMove";
    MessageId["MsgTypeFirstMoveEnd"] = "FirstMoveEnd";
    MessageId["MsgTypeUserPosList"] = "UserPosList";
    MessageId["MsgTypeRollDice"] = "RollDice";
    MessageId["MsgTypeMoveChess"] = "MoveChess";
    MessageId["MsgTypeUseProp"] = "UseProp";
    MessageId["MsgTypeChoiceProp"] = "ChoiceProp";
    MessageId["MsgTypeChoicePropResult"] = "ChoicePropResult";
    MessageId["MsgTypeChoiceAdvance"] = "ChoiceAdvance";
    MessageId["MsgTypeMoveChessEnd"] = "MoveChessEnd";
    MessageId["MsgTypeSettlement"] = "Settlement";
    MessageId["MsgTypeProductConfigs"] = "ProductConfigs";
    MessageId["MsgTypeBuyProduct"] = "BuyProduct";
    MessageId["MsgTypeSetSkin"] = "SetSkin";
    MessageId["MsgTypeMoveBlock"] = "MoveBlock";
    MessageId["MsgTypeScoreChg"] = "ScoreChg";
    // 扫雷游戏相关消息
    MessageId["MsgTypeNoticeRoundStart"] = "NoticeRoundStart";
    MessageId["MsgTypeNoticeActionDisplay"] = "NoticeActionDisplay";
    MessageId["MsgTypeNoticeRoundEnd"] = "NoticeRoundEnd";
    MessageId["MsgTypeNoticeFirstChoiceBonus"] = "NoticeFirstChoiceBonus";
    MessageId["MsgTypeNoticeGameEnd"] = "NoticeGameEnd";
    MessageId["MsgTypeClickBlock"] = "ClickBlock";
    // 关卡进度相关消息
    MessageId["MsgTypeExtendLevelProgress"] = "ExtendLevelProgress";
    MessageId["MsgTypeExtendLevelInfo"] = "ExtendLevelInfo";
    MessageId["MsgTypeLevelClickBlock"] = "LevelClickBlock";
    MessageId["MsgTypeLevelGameEnd"] = "LevelGameEnd";
    MessageId["MsgTypeEndLevelGame"] = "EndLevelGame";
    // AI托管相关消息
    MessageId["MsgTypeAIStatusChange"] = "AIStatusChange";
    MessageId["MsgTypeCancelAIManagement"] = "CancelAIManagement";
    // 调试相关消息
    MessageId["MsgTypeDebugShowMines"] = "DebugShowMines";
})(MessageId = exports.MessageId || (exports.MessageId = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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