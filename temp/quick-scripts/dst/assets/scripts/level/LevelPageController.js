
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var HexSingleChessBoardController_1 = require("../game/Chess/HexSingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 六边形单机模式棋盘控制器
        _this.hexSingleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        _this.exitButton = null; // 退出按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 记录最后一次点击是否是炸弹
        _this.lastClickWasBomb = false;
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 禁用返回按钮
     */
    LevelPageController.prototype.disableBackButton = function () {
        if (this.backButton) {
            // 移除所有触摸事件监听器
            this.backButton.off(cc.Node.EventType.TOUCH_START);
            this.backButton.off(cc.Node.EventType.TOUCH_END);
            this.backButton.off(cc.Node.EventType.TOUCH_CANCEL);
            // 设置按钮为半透明状态，表示禁用
            this.backButton.opacity = 128;
        }
    };
    /**
     * 启用返回按钮
     */
    LevelPageController.prototype.enableBackButton = function () {
        var _this = this;
        if (this.backButton) {
            // 恢复按钮透明度
            this.backButton.opacity = 255;
            // 重新设置返回按钮点击事件
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 检查是否是断线重连
        // 1. 有mineMap数据且包含已挖掘的方块表示断线重连
        // 2. reconnected字段为true表示断线重连
        // 3. gameStatus为1且有游戏进度表示游戏进行中（可能是断线重连）
        var hasRevealedBlocks = levelInfo.mineMap &&
            levelInfo.mineMap.revealedBlocks &&
            Array.isArray(levelInfo.mineMap.revealedBlocks) &&
            levelInfo.mineMap.revealedBlocks.length > 0;
        var isReconnect = hasRevealedBlocks ||
            levelInfo.reconnected === true;
        if (isReconnect) {
            console.log("关卡模式断线重连，恢复游戏状态");
            // 断线重连时，需要确定正确的关卡编号
            var serverLevelId = levelInfo.levelId;
            console.log("\u65AD\u7EBF\u91CD\u8FDE - \u670D\u52A1\u7AEF\u5173\u5361ID: " + serverLevelId + ", \u5F53\u524D\u524D\u7AEF\u5173\u5361: " + this.currentLevel);
            // 根据API文档，服务端的levelId可能从0开始，前端从1开始
            // 但是我们需要根据实际情况判断
            var targetLevel = this.currentLevel; // 默认使用当前设置的关卡
            // 如果服务端返回的levelId看起来是从0开始的（比当前关卡小1）
            if (serverLevelId === this.currentLevel - 1) {
                console.log("\u670D\u52A1\u7AEFlevelId\u4ECE0\u5F00\u59CB\uFF0C\u5BF9\u5E94\u524D\u7AEF\u5173\u5361" + this.currentLevel);
                targetLevel = this.currentLevel;
            }
            // 如果服务端返回的levelId看起来是从1开始的（与当前关卡相同）
            else if (serverLevelId === this.currentLevel) {
                console.log("\u670D\u52A1\u7AEFlevelId\u4ECE1\u5F00\u59CB\uFF0C\u5BF9\u5E94\u524D\u7AEF\u5173\u5361" + this.currentLevel);
                targetLevel = this.currentLevel;
            }
            // 其他情况，使用服务端返回的数据
            else {
                console.log("\u4F7F\u7528\u670D\u52A1\u7AEF\u8FD4\u56DE\u7684\u5173\u5361ID: " + serverLevelId);
                targetLevel = serverLevelId;
                this.setCurrentLevel(targetLevel);
            }
            // 断线重连时不重置关卡状态，保持当前状态
            // 重置炸弹点击标记
            this.lastClickWasBomb = false;
            // 更新地雷数UI
            this.updateMineCountUI(levelInfo.mineCount);
            // 关卡模式通常没有倒计时限制，如果有countDown字段，记录但不处理
            if (levelInfo.countDown !== undefined) {
                console.log("\u5173\u5361\u6A21\u5F0F\u65AD\u7EBF\u91CD\u8FDE\uFF0C\u670D\u52A1\u7AEF\u8FD4\u56DEcountDown: " + levelInfo.countDown + "\uFF08\u5173\u5361\u6A21\u5F0F\u901A\u5E38\u65E0\u5012\u8BA1\u65F6\uFF09");
            }
            // 使用当前设置的关卡编号进入关卡
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理断线重连
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.onExtendLevelInfoReconnect(levelInfo);
            }
        }
        else {
            console.log("关卡模式正常开始新游戏");
            // 检查服务端返回的关卡ID是否与当前设置一致
            var serverLevelId = levelInfo.levelId;
            console.log("\u65B0\u6E38\u620F - \u670D\u52A1\u7AEF\u5173\u5361ID: " + serverLevelId + ", \u5F53\u524D\u524D\u7AEF\u5173\u5361: " + this.currentLevel);
            // 根据API文档和实际情况，服务端的levelId可能从0开始
            // 如果服务端返回的levelId比当前关卡小1，说明服务端从0开始
            if (serverLevelId === this.currentLevel - 1) {
                console.log("\u670D\u52A1\u7AEFlevelId\u4ECE0\u5F00\u59CB\uFF0C\u7EE7\u7EED\u4F7F\u7528\u524D\u7AEF\u5173\u5361" + this.currentLevel);
            }
            // 如果服务端返回的levelId与当前关卡相同，说明服务端从1开始
            else if (serverLevelId === this.currentLevel) {
                console.log("\u670D\u52A1\u7AEFlevelId\u4ECE1\u5F00\u59CB\uFF0C\u7EE7\u7EED\u4F7F\u7528\u524D\u7AEF\u5173\u5361" + this.currentLevel);
            }
            // 如果差异较大，可能需要同步关卡编号
            else {
                console.log("\u5173\u5361\u7F16\u53F7\u5DEE\u5F02\u8F83\u5927\uFF0C\u670D\u52A1\u7AEF: " + serverLevelId + ", \u524D\u7AEF: " + this.currentLevel);
                // 可以选择使用服务端的数据，或者保持前端的设置
                // 这里暂时保持前端的设置，但记录警告
                console.warn("关卡编号不一致，请检查前后端同步逻辑");
            }
            // 重置关卡状态（包括清除测试预制体）
            this.resetLevelState();
            // 重置炸弹点击标记
            this.lastClickWasBomb = false;
            // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.resetBoard();
            }
            // 更新地雷数UI
            this.updateMineCountUI(levelInfo.mineCount);
            // 使用当前设置的关卡编号，而不是后端返回的levelId
            // 因为后端的levelId可能与前端的关卡编号不一致
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.onExtendLevelInfo();
            }
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.isHexLevel(levelNumber)) {
            // 六边形关卡使用六边形控制器
            if (this.hexSingleChessBoardController) {
                var hexBoardType = this.getHexBoardTypeByLevel(levelNumber);
                this.hexSingleChessBoardController.initBoard(hexBoardType);
                this.currentSingleChessBoard = this.hexSingleChessBoardController;
            }
            else {
                console.error("❌ 六边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        else {
            // 四边形关卡使用四边形控制器
            if (this.singleChessBoardController) {
                var boardType = this.getBoardTypeByLevel(levelNumber);
                this.singleChessBoardController.initBoard(boardType);
                this.currentSingleChessBoard = this.singleChessBoardController;
            }
            else {
                console.error("❌ 四边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 判断是否为六边形关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.isHexLevel = function (levelNumber) {
        // 特殊关卡使用六边形棋盘
        return levelNumber === 5 || levelNumber === 10 || levelNumber === 15 ||
            levelNumber === 20 || levelNumber === 25 || levelNumber === 30;
    };
    /**
     * 根据关卡编号获取六边形棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getHexBoardTypeByLevel = function (levelNumber) {
        // 六关对应六个六边形棋盘
        if (levelNumber === 5) {
            return "hexBoard1"; // 第1关六边形
        }
        else if (levelNumber === 10) {
            return "hexBoard2"; // 第2关六边形
        }
        else if (levelNumber === 15) {
            return "hexBoard3"; // 第3关六边形
        }
        else if (levelNumber === 20) {
            return "hexBoard4"; // 第4关六边形
        }
        else if (levelNumber === 25) {
            return "hexBoard5"; // 第5关六边形
        }
        else if (levelNumber === 30) {
            return "hexBoard6"; // 第6关六边形
        }
        return "hexBoard1"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, q = response.q, r = response.r, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            // 根据控制器类型决定坐标处理方式
            var coordX = void 0, coordY = void 0, coordQ = void 0, coordR = void 0;
            var hasValidCoord = false;
            if (isUsingHexController) {
                // 六边形控制器：优先使用六边形坐标，如果没有则将x,y映射为q,r
                if (q !== undefined && r !== undefined) {
                    coordQ = q;
                    coordR = r;
                    hasValidCoord = true;
                }
                else if (x !== undefined && y !== undefined) {
                    // 服务器返回x,y字段，但实际是六边形坐标：x=q, y=r
                    coordQ = x; // x 就是 q
                    coordR = y; // y 就是 r
                    hasValidCoord = true;
                }
            }
            else {
                // 四边形控制器：使用四边形坐标
                if (x !== undefined && y !== undefined) {
                    coordX = x;
                    coordY = y;
                    hasValidCoord = true;
                }
                else if (q !== undefined && r !== undefined) {
                    console.warn("\u26A0\uFE0F \u56DB\u8FB9\u5F62\u63A7\u5236\u5668\u6536\u5230\u516D\u8FB9\u5F62\u5750\u6807 (" + q + ", " + r + ")\uFF0C\u8FD9\u53EF\u80FD\u4E0D\u6B63\u786E");
                    hasValidCoord = false;
                }
            }
            if (hasValidCoord && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    // 检查是否点到炸弹
                    if (result === "boom" || result === "mine") {
                        this.lastClickWasBomb = true;
                    }
                    // 根据控制器类型调用对应的方法
                    if (isUsingHexController) {
                        // 六边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        // 四边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        if (isUsingHexController) {
                            // 六边形控制器使用handleChainReaction方法
                            this.currentSingleChessBoard.handleChainReaction(floodFillResults);
                        }
                        else {
                            // 四边形控制器使用handleFloodFillResults方法
                            this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                        }
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    if (isUsingHexController) {
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                }
            }
            else {
                console.warn("⚠️ 响应数据缺少有效坐标或结果信息");
                console.warn("   \u63A7\u5236\u5668\u7C7B\u578B: " + (isUsingHexController ? '六边形' : '四边形'));
                console.warn("   \u5750\u6807\u6570\u636E: x=" + x + ", y=" + y + ", q=" + q + ", r=" + r);
                console.warn("   \u7ED3\u679C: " + result);
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        var _this = this;
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 禁用返回按钮，防止游戏结束时玩家误点击
        this.disableBackButton();
        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面
        var isGameFailed = !gameEndData.success;
        var hasBombExploded = this.lastClickWasBomb ||
            (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());
        if (isGameFailed && hasBombExploded) {
            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面
            this.scheduleOnce(function () {
                _this.showLevelSettlement(gameEndData);
                // 重置标记
                _this.lastClickWasBomb = false;
            }, 1.5);
        }
        else {
            // 其他情况，立即显示结算页面
            this.showLevelSettlement(gameEndData);
            // 重置标记
            this.lastClickWasBomb = false;
        }
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        var _this = this;
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮 - 使用按压效果，模仿其他返回按钮
        if (this.exitButton) {
            Tools_1.Tools.imageButtonClick(this.exitButton.node, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
                _this.onExitButtonClick();
            });
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 退出按钮点击事件
     */
    LevelPageController.prototype.onExitButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮（虽然要退出了，但保持一致性）
        this.enableBackButton();
        // 返回到关卡选择页面（匹配界面）
        this.returnToLevelSelect();
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        // 先尝试直接创建一个测试预制体，不使用延迟
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            // 检查坐标字段
            var coordX = void 0, coordY = void 0;
            var pos = firstPosition; // 使用any类型避免TypeScript报错
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u65E0\u6CD5\u8BC6\u522B\u5750\u6807\u5B57\u6BB5:", firstPosition);
                return;
            }
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(coordX, coordY);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            var pos = position; // 使用any类型避免TypeScript报错
            // 获取坐标
            var coordX, coordY;
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u5730\u96F7\u4F4D\u7F6E" + index + "\u5750\u6807\u5B57\u6BB5\u65E0\u6548:", position);
                return;
            }
            if (index === 0) {
                // 第一个不延迟，立即执行（已经在上面处理过了，跳过）
                return;
            }
            else {
                // 其他的使用延迟 - 修复闭包问题
                var capturedX_1 = coordX; // 捕获当前值
                var capturedY_1 = coordY; // 捕获当前值
                _this.scheduleOnce(function () {
                    _this.createDebugMinePrefab(capturedX_1, capturedY_1);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标（四边形）或q坐标（六边形）
     * @param y 格子y坐标（四边形）或r坐标（六边形）
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        // 检查坐标是否有效
        if (x === undefined || y === undefined || x === null || y === null) {
            console.error("\u274C \u65E0\u6548\u7684\u5750\u6807\u53C2\u6570: x=" + x + ", y=" + y);
            console.error("   x\u7C7B\u578B: " + typeof x + ", y\u7C7B\u578B: " + typeof y);
            console.error("   \u8C03\u7528\u5806\u6808:", new Error().stack);
            return;
        }
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        try {
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            var debugNode = null;
            if (isUsingHexController) {
                // 六边形控制器：x实际是q，y实际是r
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, // 对于六边形，x就是q，y就是r
                this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            else {
                // 四边形控制器
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(HexSingleChessBoardController_1.default)
    ], LevelPageController.prototype, "hexSingleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "exitButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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