
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fece9VdqbVCdoFnIuu8FUgl', 'PlayerScoreController');
// scripts/pfb/PlayerScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerScoreController = /** @class */ (function (_super) {
    __extends(PlayerScoreController, _super);
    function PlayerScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.nameLabel = null; //用户昵称
        // 分数显示相关节点
        _this.scoreBgMy = null; //我的分数背景节点 score_bg_my
        _this.scoreBgOthers = null; //其他人的分数背景节点 score_bg_others
        // 加减分效果节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        // AI托管显示节点
        _this.aiManagedNode = null; //AI托管显示节点
        // 当前用户数据
        _this.currentUser = null;
        return _this;
    }
    PlayerScoreController.prototype.start = function () {
        // 初始化时隐藏所有加减分效果和托管显示
        this.hideScoreEffects();
        this.hideAIManagedNode();
    };
    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    PlayerScoreController.prototype.setData = function (user) {
        this.currentUser = user;
        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
            this.hideAIManagedNode();
        }
        else {
            // 设置头像和昵称
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            var nicknameLabel = this.nameLabel.getComponent(NickNameLabel_1.default);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;
            // 设置分数显示
            this.updateScore(user.score || 0);
        }
    };
    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    PlayerScoreController.prototype.updateScore = function (score) {
        if (!this.currentUser)
            return;
        var isMyself = this.isCurrentUser(this.currentUser.userId);
        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        }
        else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    };
    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    PlayerScoreController.prototype.isCurrentUser = function (userId) {
        var _a, _b;
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        return userId === currentUserId;
    };
    /**
     * 显示我的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showMyScore = function (score) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            var myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                var labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showOthersScore = function (score) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            var otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                var labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    };
    /**
     * 隐藏所有分数背景
     */
    PlayerScoreController.prototype.hideAllScoreBackgrounds = function () {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    PlayerScoreController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            this.addScoreNode.active = true;
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.addScoreNode) {
                    _this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerScoreController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            this.subScoreNode.active = true;
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 隐藏加减分效果节点
     */
    PlayerScoreController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    /**
     * 显示AI托管节点
     */
    PlayerScoreController.prototype.showAIManagedNode = function () {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = true;
        }
    };
    /**
     * 隐藏AI托管节点
     */
    PlayerScoreController.prototype.hideAIManagedNode = function () {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = false;
        }
    };
    /**
     * 设置AI托管状态
     * @param isManaged 是否进入AI托管状态
     */
    PlayerScoreController.prototype.setAIManagedStatus = function (isManaged) {
        if (isManaged) {
            this.showAIManagedNode();
        }
        else {
            this.hideAIManagedNode();
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Label)
    ], PlayerScoreController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgMy", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgOthers", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "subScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "aiManagedNode", void 0);
    PlayerScoreController = __decorate([
        ccclass
    ], PlayerScoreController);
    return PlayerScoreController;
}(cc.Component));
exports.default = PlayerScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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