
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/zh_CN.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '83ee5xvZ1VBLb3UezEVIqou', 'zh_CN');
// resources/i18n/zh_CN.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',
    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',
    upSeat: '加入游戏',
    downSeat: '退出游戏',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入',
    create: '创建',
    auto: '匹配',
    Room: '房间',
    room_number: '房间号',
    copy: '复制',
    game_amount: '游戏费用',
    player_numbers: '玩家数量:',
    room_exist: '房间不存在',
    enter_room_number: '输入房间号',
    free: '免费',
    players: '玩家',
    Player: '玩家',
    Tickets: '门票',
    Empty: '空位',
    nextlevel: '下一关',
    relevel: '再玩一次',
    boting: "托管中...",
    roundstart: "新的一轮开始，请扫雷",
    rules: '规则',
    danjiguize: "单机规则",
    lianjuguize: "联机规则",
    dtips1: "游戏简介：",
    dtips2: "安全区：",
    dtips3: "雷区：",
    dtips4: "游戏目标：",
    dtips5: "标记：",
    dtips6: "提示：",
    dinfo1: "游戏中存在一些格子，翻开后分为数字格子，空白格子和地雷，玩家可以通过点击翻开新格子，数字格子可以同帮助玩家获得周围相邻格子中存在的地雷数量，若翻到空白格子会继续扩散翻牌，直到翻到数字格子才停止，利用游戏规则，顺利通关吧。",
    dinfo2: "数字格子与空白格子统称为安全区",
    dinfo3: "翻到雷区，会导致游戏失败。",
    dinfo4: "在不触发任何雷区的情况下，揭示游戏区域中的所有安全格子。",
    dinfo5: "玩家可以通过对格子长按，插旗子来标记雷区，标记不会翻开该格子。",
    dinfo6: "玩家每局可获得一次提示，仅本局生效，点击提示可以帮助玩家显示出一个安全的格子。一局最多使用4次提示。",
    ltips1: "游戏简介：",
    ltips2: "游戏人数：",
    ltips3: "回合时间：",
    ltips4: "游戏目标：",
    ltips5: "标记：",
    ltips6: "得分规则：",
    linfo1: "与单机模式相同，格子的样式一致。每个回合所有玩家同时选择任意一个格子，时间结束后或所有人全部选择完毕后，展示所有人的选择情况并根据格子的内容进行加减分，待所有格子都被选完，游戏结束。争取得到更多的分数吧！",
    linfo2: "2人/3人/4人",
    linfo3: "20秒",
    linfo4: "利用游戏规则，进行得分，分数最高者获得胜利。",
    linfo5: "长按可对格子进行雷区标记，若该格子为雷区，则可额外获得加分。",
    linfo6: "1. 每回合最先选择格子的玩家获得1分。\n2. 单击翻开格子，若为安全格子则加6分，为雷区则扣12分。\n3. 长按进行标记翻开格子，若为雷区则加10分，为安全区则不加分。\n4. 多人同时选择一个格子，根据选择的对错结果，进行平均加分，例如两人单击选择同一个格子，该格子为安全区，则每人得3分。",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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