
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/en.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    nextlevel: 'Next',
    relevel: 'Play Again',
    rules: 'Rules',
    danjiguize: "Single Player Rules",
    lianjuguize: "Multiplayer Rules",
    boting: "Hosting...",
    roundstart: "New Round Starts",
    dtips1: "Game Introduction:",
    dtips2: "Safe Zone:",
    dtips3: "Mine Zone:",
    dtips4: "Game Objective:",
    dtips5: "Marking:",
    dtips6: "Hint:",
    dinfo1: "The game contains hidden tiles that can be: numbered tiles, blank tiles, or mines. Click to reveal tiles - numbered tiles indicate how many mines are adjacent to that tile. Blank tiles will trigger a chain reaction of automatic reveals until numbered tiles are encountered. Use these mechanics to clear the board.",
    dinfo2: "Numbered tiles and blank tiles are collectively called the Safe Zone.",
    dinfo3: "Revealing a mine tile will cause instant failure.",
    dinfo4: "Reveal all safe tiles without triggering any mines to win.",
    dinfo5: "Long-press to place a flag marker on suspected mine tiles. Marking doesn't reveal the tile.",
    dinfo6: "Players get one free hint per game (max 4 uses). Clicking Hint will reveal one safe tile. Hints are game-specific and don't carry over.",
    ltips1: "Game Introduction:",
    ltips2: "Player Count:",
    ltips3: "Turn Duration:",
    ltips4: "Game Objective:",
    ltips5: "Marking:",
    ltips6: "Scoring Rules:",
    linfo1: "Uses the same tile mechanics as Single Player mode. Each turn, all players simultaneously select tiles. After time expires or all players finish choosing, results are revealed with score adjustments. Game ends when all tiles are claimed. Compete for the highest score!",
    linfo2: "2/3/4 Players",
    linfo3: "20 Seconds",
    linfo4: "Score points through gameplay mechanics to win.",
    linfo5: "Long-press to mark potential mines. Correct mine markings grant bonus points.",
    linfo6: "1. First player to select a tile each turn earns +1pt.\n2. Click-reveal: +6pts for safe tiles, -12pts for mines.\n3. Mark-reveal: +10pts for correctly flagged mines, 0pts for safe tiles.\n4. Shared tiles: Points are split (e.g., two players click same safe tile = +3pts each)."
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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