
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/zh_HK.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'df865DJwGFNyZgxO3JjZjqY', 'zh_HK');
// resources/i18n/zh_HK.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: '退出後將無法返回遊戲。',
    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',
    upSeat: '加入遊戲',
    downSeat: '退出遊戲',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join: '進入',
    create: '創建',
    auto: '匹配',
    Room: '房間',
    room_number: '房號',
    copy: '複製',
    game_amount: '遊戲費用',
    player_numbers: '玩家數量:',
    room_exist: '房間不存在',
    enter_room_number: '輸入房間號',
    free: '免費',
    players: '玩家',
    Player: '玩家',
    Tickets: '門票',
    Empty: '空位',
    nextlevel: '下一關',
    relevel: '再玩一次',
    rules: '規則',
    danjiguize: "單機規則",
    lianjuguize: "聯機規則",
    boting: "託管中...",
    roundstart: "新的一輪開始，請掃雷",
    dtips1: "遊戲簡介：",
    dtips2: "安全區：",
    dtips3: "雷區：",
    dtips4: "遊戲目標：",
    dtips5: "標記：",
    dtips6: "提示：",
    dinfo1: "遊戲中存在一些格子，翻開後分為數字格子、空白格子和地雷。玩家可以通過點擊翻開新格子，數字格子可以幫助玩家獲得周圍相鄰格子中存在的地雷數量。若翻到空白格子會繼續擴散翻牌，直到翻到數字格子才停止。利用遊戲規則，順利通關吧。",
    dinfo2: "數字格子與空白格子統稱為安全區。",
    dinfo3: "翻到雷區，會導致遊戲失敗。",
    dinfo4: "在不觸發任何雷區的情況下，揭示遊戲區域中的所有安全格子。",
    dinfo5: "玩家可以通過對格子長按，插旗子來標記雷區，標記不會翻開該格子。",
    dinfo6: "玩家每局可獲得一次提示，僅本局生效。點擊提示可以幫助玩家顯示出一個安全的格子。一局最多使用4次提示。",
    ltips1: "遊戲簡介：",
    ltips2: "遊戲人數：",
    ltips3: "回合時間：",
    ltips4: "遊戲目標：",
    ltips5: "標記：",
    ltips6: "得分規則：",
    linfo1: "與單機模式相同，格子的樣式一致。每個回合所有玩家同時選擇任意一個格子，時間結束後或所有人全部選擇完畢後，展示所有人的選擇情況並根據格子的內容進行加減分。待所有格子都被選完，遊戲結束。爭取得到更多的分數吧！",
    linfo2: "2人/3人/4人",
    linfo3: "20秒",
    linfo4: "利用遊戲規則進行得分，分數最高者獲得勝利。",
    linfo5: "長按可對格子進行雷區標記，若該格子為雷區，則可額外獲得加分。",
    linfo6: "1. 每回合最先選擇格子的玩家獲得1分。\n2. 單擊翻開格子，若為安全格子則加6分，為雷區則扣12分。\n3. 長按進行標記翻開格子，若為雷區則加10分，為安全區則不加分。\n4. 多人同時選擇一個格子，根據選擇的對錯結果進行平均加分。例如兩人單擊選擇同一個格子，該格子為安全區，則每人得3分。"
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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