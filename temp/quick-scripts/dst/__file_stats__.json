{"version": "1.0.8", "stats": {"/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/__qc_index__.js": "2025-08-04T09:46:46.734Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/joui18n-script/LocalizedSprite.js": "2025-08-04T09:46:46.682Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/joui18n-script/LocalizedLabel.js": "2025-08-04T09:46:46.691Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/meshTools/tools/MeshSdk.js": "2025-08-04T09:46:46.700Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/bean/GameBean.js": "2025-08-04T09:46:46.692Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/Chess/GridController.js": "2025-08-04T09:46:46.686Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/Level/LevelSelectController.js": "2025-08-04T09:46:46.684Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/meshTools/Singleton.js": "2025-08-04T09:46:46.689Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/resources/i18n/zh_HK.js": "2025-08-04T09:46:46.706Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/common/GameMgr.js": "2025-08-04T09:46:46.678Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/GameServerUrl.js": "2025-08-04T09:46:46.682Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/level/LevelPageController.js": "2025-08-04T09:46:46.701Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/InfoItemOneController.js": "2025-08-04T09:46:46.679Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/start_up/StartUpPageController.js": "2025-08-04T09:46:46.710Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/AudioMgr.js": "2025-08-04T09:46:46.694Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/meshTools/BaseSDK.js": "2025-08-04T09:46:46.699Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/meshTools/tools/Publish.js": "2025-08-04T09:46:46.680Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/TipsDialogController.js": "2025-08-04T09:46:46.708Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/ToastController.js": "2025-08-04T09:46:46.692Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/bean/LanguageType.js": "2025-08-04T09:46:46.690Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/common/GameTools.js": "2025-08-04T09:46:46.712Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/bean/EnumBean.js": "2025-08-04T09:46:46.694Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/meshTools/tools/MeshSdkApi.js": "2025-08-04T09:46:46.710Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/common/GameData.js": "2025-08-04T09:46:46.687Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/GlobalManagerController.js": "2025-08-04T09:46:46.674Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/common/MineConsole.js": "2025-08-04T09:46:46.699Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/BtnController.js": "2025-08-04T09:46:46.677Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/bean/GlobalBean.js": "2025-08-04T09:46:46.695Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/CongratsDialogController.js": "2025-08-04T09:46:46.710Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/Chess/HexSingleChessBoardController.js": "2025-08-04T09:46:46.677Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/Chess/SingleChessBoardController.js": "2025-08-04T09:46:46.675Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/AIManagedDialogController.js": "2025-08-04T09:46:46.685Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/HallCreateRoomController.js": "2025-08-04T09:46:46.683Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/GamePageController.js": "2025-08-04T09:46:46.696Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/HallJoinRoomController.js": "2025-08-04T09:46:46.693Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/HallPageController.js": "2025-08-04T09:46:46.688Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/HallParentController.js": "2025-08-04T09:46:46.701Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/InfoDialogController.js": "2025-08-04T09:46:46.686Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/KickOutDialogController.js": "2025-08-04T09:46:46.676Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/LevelSelectDemo.js": "2025-08-04T09:46:46.711Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/Chess/HexChessBoardController.js": "2025-08-04T09:46:46.704Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/Chess/ChessBoardController.js": "2025-08-04T09:46:46.698Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/game/GameScoreController.js": "2025-08-04T09:46:46.708Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/PlayerLayoutController.js": "2025-08-04T09:46:46.681Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/SettingDialogController.js": "2025-08-04T09:46:46.689Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/HallAutoController.js": "2025-08-04T09:46:46.712Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/TopUpDialogController.js": "2025-08-04T09:46:46.703Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/Level/LevelSelectExample.js": "2025-08-04T09:46:46.695Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/LeaveDialogController.js": "2025-08-04T09:46:46.706Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/Level/LevelSelectPageController.js": "2025-08-04T09:46:46.708Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/Level/LevelItemController.js": "2025-08-04T09:46:46.687Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/Level/ScrollViewHelper.js": "2025-08-04T09:46:46.707Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/HttpManager.js": "2025-08-04T09:46:46.691Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/HttpUtils.js": "2025-08-04T09:46:46.698Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/MessageBaseBean.js": "2025-08-04T09:46:46.683Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/WebSocketManager.js": "2025-08-04T09:46:46.700Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/IHttpMsgBody.js": "2025-08-04T09:46:46.681Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/WebSocketTool.js": "2025-08-04T09:46:46.675Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/common/EventCenter.js": "2025-08-04T09:46:46.711Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/HallCenterLayController.js": "2025-08-04T09:46:46.707Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/PlayerGameController .js": "2025-08-04T09:46:46.702Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/InfoItemController.js": "2025-08-04T09:46:46.684Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/PlayerScoreController.js": "2025-08-04T09:46:46.712Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/MessageId.js": "2025-08-04T09:46:46.709Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/CongratsItemController.js": "2025-08-04T09:46:46.686Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/start_up/StartUpCenterController.js": "2025-08-04T09:46:46.693Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/SeatItemController.js": "2025-08-04T09:46:46.683Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/net/ErrorCode.js": "2025-08-04T09:46:46.679Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/Config.js": "2025-08-04T09:46:46.681Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/hall/MatchParentController.js": "2025-08-04T09:46:46.689Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/Tools.js": "2025-08-04T09:46:46.688Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/AudioManager.js": "2025-08-04T09:46:46.685Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/meshTools/MeshTools.js": "2025-08-04T09:46:46.680Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/pfb/MatchItemController.js": "2025-08-04T09:46:46.678Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/NickNameLabel.js": "2025-08-04T09:46:46.706Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/LocalStorageManager.js": "2025-08-04T09:46:46.687Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/resources/i18n/zh_CN.js": "2025-08-04T09:46:46.691Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/Dictionary.js": "2025-08-04T09:46:46.690Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/resources/i18n/en.js": "2025-08-04T09:46:46.702Z", "/Users/<USER>/Documents/Minesweeper/temp/quick-scripts/src/assets/scripts/util/BlockingQueue.js": "2025-08-04T09:46:46.684Z"}}