"use strict";
cc._RF.push(module, '617b5KPPRpFBp3JOQImy4wR', 'Tools');
// scripts/util/Tools.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tools = void 0;
var AudioManager_1 = require("./AudioManager");
var Config_1 = require("./Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var Tools = /** @class */ (function () {
    function Tools() {
    }
    Tools.setTouchEvent = function (peopleNode, startFunction, endFunction, cancelFunction) {
        this.setTouchEventParent(peopleNode, true, startFunction, endFunction, cancelFunction);
    };
    Tools.setGameTouchEvent = function (peopleNode, startFunction, endFunction, cancelFunction) {
        this.setTouchEventParent(peopleNode, false, startFunction, endFunction, cancelFunction);
    };
    //添加点击事件 
    //isSound 是否需要按键音效，大厅的都需要 游戏内有自己的按键音所以不需要
    //peopleNode 节点
    //startFunction 按下事件
    //endFunction 抬起事件
    //cancelFunction 取消事件
    Tools.setTouchEventParent = function (peopleNode, isSound, startFunction, endFunction, cancelFunction) {
        peopleNode.on(cc.Node.EventType.TOUCH_START, function (event) {
            if (isSound) {
                AudioManager_1.AudioManager.keyingToneAudio();
            }
            if (startFunction != null) {
                startFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            if (endFunction != null) {
                endFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_CANCEL, function (event) {
            if (cancelFunction != null) {
                cancelFunction(peopleNode, event);
            }
        }, this);
    };
    Tools.cancelTouchStartListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_START, this);
    };
    Tools.cancelTouchEndListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_END, this);
    };
    Tools.cancelTouchCancelListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_CANCEL, this);
    };
    //为精灵添加图片
    Tools.setNodeSpriteFrame = function (node, path) {
        cc.resources.load(path, cc.SpriteFrame, function (error, assets) {
            var sprite = node.getComponent(cc.Sprite);
            sprite.spriteFrame = assets;
        });
    };
    //添加网络图片
    Tools.setNodeSpriteFrameUrl = function (node, url) {
        if (!node) {
            return;
        }
        var avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }
        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u56FE\u7247\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                return;
            }
            texture.setPremultiplyAlpha(true); // 👈 关键设置
            texture.packable = false; //加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            node.active = true;
            node.opacity = 255;
        });
    };
    // 设置备用纹理
    Tools.setFallbackTexture = function (sprite) {
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var color = [150, 150, 150, 255]; // 灰色
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
    };
    // 异步加载网络图片（带回调）
    Tools.setNodeSpriteFrameUrlAsync = function (node, url, onComplete) {
        if (!node) {
            console.error("❌ 节点为null，无法设置图片");
            if (onComplete)
                onComplete(false);
            return;
        }
        var avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }
        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            if (onComplete)
                onComplete(false);
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u56FE\u7247\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                if (onComplete)
                    onComplete(false);
                return;
            }
            texture.setPremultiplyAlpha(true); // 👈 关键设置
            texture.packable = false; //加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            node.active = true;
            node.opacity = 255;
            if (onComplete)
                onComplete(true);
        });
    };
    //红色按钮
    Tools.redButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnRedNormal, Config_1.Config.btnRedPressed, Config_1.Config.btnRedNormalColor, Config_1.Config.btnRedPressedColor, click, label);
    };
    //绿色按钮
    Tools.greenButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnGreenNormal, Config_1.Config.btnGreenPressed, Config_1.Config.btnGreenNormalColor, Config_1.Config.btnGreenPressedColor, click, label);
    };
    //黄色按钮
    Tools.yellowButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnYellowNormal, Config_1.Config.btnYellowPressed, Config_1.Config.btnYellowNormalColor, Config_1.Config.btnYellowPressedColor, click, label);
    };
    //灰色按钮
    Tools.grayButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnGrayNormal, Config_1.Config.btnGrayNormal, Config_1.Config.btnGrayNormalColor, Config_1.Config.btnGrayNormalColor, click, label);
    };
    //通用的按钮点击事件，带点击变颜色的
    Tools.buttonState = function (node, normalImg, pressedImg, normalColor, pressedColor, click, labelText) {
        var btnGreen = node.getChildByName('btn_color_normal'); //获取按钮背景节点
        var btnLabel = node.getChildByName('button_label'); //获取按钮文字节点
        var label = btnLabel.getComponent(cc.Label);
        var labelOutline = btnLabel.getComponent(cc.LabelOutline);
        if (labelText != null) {
            label.string = labelText;
        }
        Tools.setTouchEvent(btnGreen, function (node) {
            Tools.setNodeSpriteFrame(node, pressedImg);
            label.fontSize = 34;
            label.lineHeight = 34;
            var color = new cc.Color();
            cc.Color.fromHEX(color, pressedColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            if (click != null) {
                click();
            }
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
        });
    };
    //点击变颜色的图片按钮
    Tools.imageButtonClick = function (node, normalImg, pressedImg, click) {
        Tools.setTouchEvent(node, function (node) {
            Tools.setNodeSpriteFrame(node, pressedImg);
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            click();
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
        });
    };
    //格式化资金显示格式的
    Tools.NumToTBMK = function (num, digit, min) {
        var _a;
        if (digit === void 0) { digit = 1; }
        if (min === void 0) { min = 10000; }
        var intNum = num;
        if (intNum < min) {
            return intNum.toString();
        }
        var unitStrArr = ["T", "B", "M", "K"];
        var unitArr = [Math.pow(10, 12), Math.pow(10, 9), Math.pow(10, 6), Math.pow(10, 3)];
        for (var i = 0; i < unitArr.length; ++i) {
            var result = intNum / unitArr[i];
            if (result >= 1) {
                var str = result.toString();
                var strArr = str.split(".");
                var suffix = (_a = strArr[1]) !== null && _a !== void 0 ? _a : "";
                if (suffix.length >= digit) {
                    if (digit == 0) {
                        return strArr[0] + unitStrArr[i];
                    }
                    return strArr[0] + "." + suffix.substring(0, digit) + unitStrArr[i];
                }
                else {
                    var fillStr = new Array(digit - suffix.length).fill("0").join("");
                    return strArr[0] + "." + suffix + fillStr + unitStrArr[i];
                }
            }
        }
    };
    Tools.getCurrentTimeWithMilliseconds = function () {
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        var month = String(currentDate.getMonth() + 1).padStart(2, '0');
        var day = String(currentDate.getDate()).padStart(2, '0');
        var hours = String(currentDate.getHours()).padStart(2, '0');
        var minutes = String(currentDate.getMinutes()).padStart(2, '0');
        var seconds = String(currentDate.getSeconds()).padStart(2, '0');
        var milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0');
        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds + "." + milliseconds;
    };
    //赋值文本到剪切板
    Tools.copyToClipboard = function (text) {
        var textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                console.log('文本已复制到剪切板');
            }
            else {
                console.error('复制到剪切板失败');
            }
        }
        catch (err) {
            console.error('复制到剪切板失败：', err);
        }
        document.body.removeChild(textarea);
    };
    //拆分数组用的，一个长度为 10 的数组 拆分成 2 个长度为 5 的数组 chunkArray(user_s, 5)
    Tools.chunkArray = function (arr, chunkSize) {
        var result = [];
        for (var i = 0; i < arr.length; i += chunkSize) {
            result.push(arr.slice(i, i + chunkSize));
        }
        return result;
    };
    //设置倒计时的秒数的位置
    Tools.setCountDownTimeLabel = function (buttonNode) {
        var btn = buttonNode.getChildByName('button_label');
        var timeBtn = buttonNode.getChildByName('buttonLabel_time');
        // buttonLabel_time 紧贴着 button_label 的右边
        // button_label的右边界 + buttonLabel_time宽度的一半 = buttonLabel_time的中心位置
        var xPos = btn.position.x + btn.width / 2 + timeBtn.width / 2;
        timeBtn.setPosition(xPos, 0);
    };
    return Tools;
}());
exports.Tools = Tools;

cc._RF.pop();