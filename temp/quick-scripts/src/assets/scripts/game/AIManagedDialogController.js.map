{"version": 3, "sources": ["assets/scripts/game/AIManagedDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,aAAa;AACb,kCAAkC;;;;;;;;;;;;;;;;;;;;;AAElC,4DAA2D;AAC3D,8CAA6C;AAC7C,yCAAwC;AAElC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAuD,6CAAY;IAAnE;QAAA,qEAsOC;QAnOG,aAAO,GAAY,IAAI,CAAC,CAAC,MAAM;QAG/B,cAAQ,GAAY,IAAI,CAAC,CAAC,gBAAgB;QAIlC,eAAS,GAAY,KAAK,CAAC,CAAC,SAAS;;IA4NjD,CAAC;IA1NG,yCAAK,GAAL;QAGI,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAIzB,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAEf,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;aAEzB;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAClC;SACJ;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,oCAAoC;YACpC,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,gBAAgB,EAAE;gBAElB,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;aACpC;YAED,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SAEzE;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAChC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,wCAAI,GAAJ;QAGI,IAAI,IAAI,CAAC,SAAS,EAAE;YAEhB,OAAO;SACV;QAGD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAExB,gCAAgC;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;aAEpC;SACJ;QAID,UAAU;QACV,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;SAE5B;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACnC;QAED,SAAS;QACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAE7B,CAAC;IAED;;OAEG;IACH,wCAAI,GAAJ;QAAA,iBAoBC;QAnBG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;aAEnC;SACJ;QAED,SAAS;QACT,IAAI,CAAC,iBAAiB,CAAC;YACnB,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,qDAAiB,GAAzB;QAGI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpC,OAAO;SACV;QAID,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,GAAG;SACf,EAAE;YACC,MAAM,EAAE,SAAS;SACpB,CAAC;aACD,IAAI,CAAC;QAEN,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,qDAAiB,GAAzB,UAA0B,QAAqB;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,QAAQ;gBAAE,QAAQ,EAAE,CAAC;YACzB,OAAO;SACV;QAED,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;SACb,EAAE;YACC,MAAM,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC;YACF,IAAI,QAAQ;gBAAE,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QAEI,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QAEI,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAEjB,OAAO;SACV;QAID,aAAa;QACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,mBAAmB;QACnB,IAAI,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,IAAM,UAAU,GAAG;QACf,eAAe;SAClB,CAAC;QAEF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;IAE5F,CAAC;IAED;;OAEG;IACH,6CAAS,GAAT;QACI,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9C,CAAC;IAED,6CAAS,GAAT;QACI,SAAS;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SAC1E;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IAlOD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACM;IAGxB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACO;IANR,yBAAyB;QAD7C,OAAO;OACa,yBAAyB,CAsO7C;IAAD,gCAAC;CAtOD,AAsOC,CAtOsD,EAAE,CAAC,SAAS,GAsOlE;kBAtOoB,yBAAyB", "file": "", "sourceRoot": "/", "sourcesContent": ["// AI托管中页面控制器\n// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息\n\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\nimport { Config } from \"../util/Config\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class AIManagedDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null; // 背景板\n\n    @property(cc.Node)\n    maskNode: cc.Node = null; // 遮罩节点，用于接收点击事件\n\n   \n\n    private isShowing: boolean = false; // 是否正在显示\n\n    start() {\n       \n\n        // 初始化时隐藏\n        this.node.active = false;\n\n        \n\n        // 如果 boardBg 没有设置，尝试查找子节点\n        if (!this.boardBg) {\n         \n            const bgNode = this.node.getChildByName('bot_bg');\n            if (bgNode) {\n                this.boardBg = bgNode;\n              \n            } else {\n                console.warn(\"未找到 bot_bg 子节点\");\n            }\n        }\n\n        // 为遮罩节点添加点击事件监听\n        if (this.maskNode) {\n            // 检查是否有 BlockInputEvents 组件，如果有则禁用它\n            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);\n            if (blockInputEvents) {\n               \n                blockInputEvents.enabled = false;\n            }\n\n            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);\n           \n        } else {\n            console.warn(\"maskNode 未设置\");\n        }\n\n        // 同时为主节点添加点击事件监听作为备用\n        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);\n    }\n\n    /**\n     * 显示托管中页面\n     */\n    show() {\n       \n\n        if (this.isShowing) {\n            \n            return;\n        }\n\n      \n        this.isShowing = true;\n        this.node.active = true;\n\n        // 禁用 BlockInputEvents 组件以允许点击事件\n        if (this.maskNode) {\n            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);\n            if (blockInputEvents) {\n                blockInputEvents.enabled = false;\n                \n            }\n        }\n\n        \n\n        // 初始化动画状态\n        if (this.boardBg) {\n            this.boardBg.scale = 0;\n            this.boardBg.opacity = 0;\n           \n        } else {\n            console.warn(\"❌ boardBg 节点未设置\");\n        }\n\n        // 执行显示动画\n        this.playShowAnimation();\n        \n    }\n\n    /**\n     * 隐藏托管中页面\n     */\n    hide() {\n        if (!this.isShowing) {\n            return;\n        }\n\n        this.isShowing = false;\n\n        // 重新启用 BlockInputEvents 组件（如果存在）\n        if (this.maskNode) {\n            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);\n            if (blockInputEvents) {\n                blockInputEvents.enabled = true;\n               \n            }\n        }\n\n        // 执行隐藏动画\n        this.playHideAnimation(() => {\n            this.node.active = false;\n        });\n    }\n\n    /**\n     * 播放显示动画\n     */\n    private playShowAnimation() {\n        \n\n        if (!this.boardBg) {\n            console.warn(\"❌ boardBg 为空，无法播放动画\");\n            return;\n        }\n\n       \n\n        // 缩放和透明度动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, {\n                scale: 1,\n                opacity: 255\n            }, {\n                easing: 'backOut'\n            })\n            .call(() => {\n               \n            })\n            .start();\n    }\n\n    /**\n     * 播放隐藏动画\n     * @param callback 动画完成回调\n     */\n    private playHideAnimation(callback?: () => void) {\n        if (!this.boardBg) {\n            if (callback) callback();\n            return;\n        }\n\n        // 缩放和透明度动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { \n                scale: 0, \n                opacity: 0 \n            }, { \n                easing: 'backIn' \n            })\n            .call(() => {\n                if (callback) callback();\n            })\n            .start();\n    }\n\n    /**\n     * 遮罩点击事件处理\n     */\n    private onMaskClick() {\n        \n        this.handleClick();\n    }\n\n    /**\n     * 主节点点击事件处理\n     */\n    private onNodeClick() {\n       \n        this.handleClick();\n    }\n\n    /**\n     * 统一的点击处理逻辑\n     */\n    private handleClick() {\n        if (!this.isShowing) {\n           \n            return;\n        }\n\n       \n\n        // 发送取消AI托管消息\n        this.sendCancelAIManagement();\n\n        // 立即隐藏页面（不等待服务器响应）\n        this.hide();\n    }\n\n    /**\n     * 发送取消AI托管消息\n     */\n    private sendCancelAIManagement() {\n        const cancelData = {\n            // 可以根据需要添加其他参数\n        };\n\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelAIManagement, cancelData);\n      \n    }\n\n    /**\n     * 检查是否正在显示\n     */\n    isVisible(): boolean {\n        return this.isShowing && this.node.active;\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.maskNode) {\n            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n\n        if (this.node) {\n            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);\n        }\n    }\n}\n"]}